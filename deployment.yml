---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agentous-web-app
  annotations:
    kubernetes.io/ingress.class: 'alb'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healty-threshold-count: '1'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP":80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/backend-protocol: 'HTTP'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:128307813579:certificate/31c20319-c86c-49d8-ae49-c70dbdf03637
spec:
  rules:
  - host: dev.agentous.ai
    http:
      paths:
      - pathType: Prefix
        path: /
        backend:
          service:
            name: agentous-web-app
            port:
              number: 443
---
apiVersion: v1
kind: Service
metadata:
  name: agentous-web-app
spec:
  type: NodePort
  selector:
    app: agentous-web-app
  ports:
    - name: https
      protocol: TCP
      port: 443
      targetPort: 3000
    - name: http
      protocol: TCP
      port: 80
      targetPort: 3000
---
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: agentous-web-app
  name: agentous-web-app
spec:
  maxReplicas: 1
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agentous-web-app
  targetCPUUtilizationPercentage: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agentous-web-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: agentous-web-app
  template:
    metadata:
      labels:
        app: agentous-web-app
    spec:
      containers:
        - name: agentous-web-app
          image: {{image}}
          imagePullPolicy: Always
          resources:
            requests:
              cpu: '150m'
          ports:
            - containerPort: 3000
              protocol: TCP
          