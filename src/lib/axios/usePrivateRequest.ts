import { isDevEnvironment } from "@/utils/helpers";
import { useKeycloak } from "@react-keycloak/web";
import type { AxiosInstance } from "axios";
import axios from "axios";
import { useEffect, useRef } from "react";

export const usePrivateRequest = (baseURL: string) => {
  const axiosInstance = useRef<AxiosInstance>();
  const { keycloak, initialized } = useKeycloak();

  const kcToken = keycloak?.token ?? import.meta.env.VITE_DEV_ACCESS_TOKEN;

  useEffect(() => {
    axiosInstance.current = axios.create({
      baseURL,
      headers: {
        Authorization:
          initialized || isDevEnvironment() ? `Bearer ${kcToken}` : undefined,
      },
      timeout: 120000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });

    axiosInstance.current.defaults.headers.common["Save-Data"] = "on";

    axiosInstance.current.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.code === "ECONNABORTED") {
          console.warn("Request timed out. Retrying...");
          return axiosInstance.current?.request(error.config); // Retry
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axiosInstance.current = undefined;
    };
  }, [baseURL, initialized, kcToken]);

  return axiosInstance;
};

export const usePivotlPrivateRequest = (
  baseURL: string,
  tenentId: string,
  agent: string
) => {
  const axiosInstance = useRef<AxiosInstance>();
  const { keycloak, initialized } = useKeycloak();

  const kcToken = keycloak?.token ?? import.meta.env.VITE_DEV_ACCESS_TOKEN;

  useEffect(() => {
    axiosInstance.current = axios.create({
      baseURL,
      headers: {
        Authorization:
          initialized || isDevEnvironment() ? `Bearer ${kcToken}` : undefined,
        "X-Active-Tenant": tenentId,
        "X-Active-Agent": agent,
      },
      timeout: 120000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });

    axiosInstance.current.defaults.headers.common["Save-Data"] = "on";

    axiosInstance.current.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.code === "ECONNABORTED") {
          console.warn("Request timed out. Retrying...");
          return axiosInstance.current?.request(error.config); // Retry
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axiosInstance.current = undefined;
    };
  }, [baseURL, initialized, kcToken, tenentId, agent]);

  return axiosInstance;
};

// New hook for unauthenticated requests with only X-Active-Agent header
export const useUnauthenticatedAgentRequest = (
  baseURL: string,
  agent: string
) => {
  const axiosInstance = useRef<AxiosInstance>();

  useEffect(() => {
    axiosInstance.current = axios.create({
      baseURL,
      headers: {
        "X-Active-Agent": agent,
      },
      timeout: 120000,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });

    axiosInstance.current.defaults.headers.common["Save-Data"] = "on";

    axiosInstance.current.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.code === "ECONNABORTED") {
          console.warn("Request timed out. Retrying...");
          return axiosInstance.current?.request(error.config); // Retry
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axiosInstance.current = undefined;
    };
  }, [baseURL, agent]);

  return axiosInstance;
};
