export const ROUTES = {
  // Base app routes
  HOME: "/",
  AGENTS: (agentId: string) => `/agents/${agentId}`,
  PRICING: "/pricing",
  PRODUCTS: "/products",
  ABOUT: "/about",
  SIGNUP: "/signup",
  LOGI<PERSON>: "/login",
  REDIRECT_HANDLER: "/redirect-handler",

  // Dashboard base (main layout)
  DASHBOARD_BASE: "/dashboard",

  // Dashboard main sections
  DASHBOARD_AI_AGENTS: "/dashboard/ai-agents",
  DASHBOARD_AGENT_SUITE: (suiteId: string) =>
    `/dashboard/ai-agents/suite/${suiteId}`,
  DASHBOARD_AGENT_ACTIVATION: "/dashboard/ai-agents/activate",
  DASHBOARD_AGENT_ACTIVATION_SUITE: (suiteId: string) =>
    `/dashboard/ai-agents/activate/suite/${suiteId}`,
  DASHBOARD_AGENT_ACTIVATION_AGENT: (agentId: string) =>
    `/dashboard/ai-agents/activate/agent/${agentId}`,

  // Business Stack routes
  DASHBOARD_BUSINESS_STACK: "/dashboard/business-stack",
  DASHBOARD_BUSINESS_STACK_SELECT_AGENT:
    "/dashboard/business-stack/select-agent",
  DASHBOARD_BUSINESS_STACK_ACTIVATE_SUITE: (suiteId: string) =>
    `/dashboard/business-stack/activate-suite/${suiteId}`,
  DASHBOARD_BUSINESS_STACK_WITH_APP: (appKey: string) =>
    `/dashboard/business-stack/${appKey}`,

  // Knowledge Base routes
  DASHBOARD_KNOWLEDGE_BASE: "/dashboard/knowledge-base",
  DASHBOARD_KNOWLEDGE_BASE_SELECT_AGENT:
    "/dashboard/knowledge-base/select-agent",
  DASHBOARD_KNOWLEDGE_BASE_ACTIVATE_SUITE: (suiteId: string) =>
    `/dashboard/knowledge-base/activate-suite/${suiteId}`,

  // Knowledge Base routes
  DASHBOARD_KNOWLEDGE_BASE_CATEGORY: (categoryId: string) =>
    `/dashboard/knowledge-base/${categoryId}`,
  DASHBOARD_KNOWLEDGE_BASE_ITEM: (categoryId: string, itemId: string) =>
    `/dashboard/knowledge-base/${categoryId}/${itemId}`,

  // Analytics section (renamed from conflicting "Dashboard")
  DASHBOARD_SELECT_AGENT: "/dashboard/analytics/select-agent",
  DASHBOARD_ANALYTICS_ACTIVATE_SUITE: (suiteId: string) =>
    `/dashboard/analytics/activate-suite/${suiteId}`,

  DASHBOARD_ANALYTICS_BASE: "/dashboard/analytics",
  DASHBOARD_ANALYTICS_SCYRA: "/dashboard/analytics/scyra",
  DASHBOARD_ANALYTICS_SETO: "/dashboard/analytics/seto",
  DASHBOARD_ANALYTICS_LIORA: "/dashboard/analytics/liora",

  // New analytics structure
  DASHBOARD_ANALYTICS_INSIGHTS: "/dashboard/analytics/insights",
  DASHBOARD_ANALYTICS_INSIGHTS_ALL: "/dashboard/analytics/insights/all",
  DASHBOARD_ANALYTICS_TASK_LOGS: "/dashboard/analytics/task-logs",
  DASHBOARD_ANALYTICS_TASK_LOG_DETAILS: (taskId: string) =>
    `/dashboard/analytics/task-logs/${taskId}`,
  DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS: "/dashboard/analytics/assignment-logs",

  // Legacy setiq routes (for backwards compatibility)
  DASHBOARD_ANALYTICS_SETIQ_DASHBOARD: "/dashboard/analytics/setiq",
  DASHBOARD_ANALYTICS_SETIQ_COLLECTOR_PERFORMANCE:
    "/dashboard/analytics/setiq/collector-performance",
  DASHBOARD_ANALYTICS_SETIQ_DAILY_INSIGHTS:
    "/dashboard/analytics/setiq/daily-insights",

  // Settings section
  DASHBOARD_SETTINGS: "/dashboard/settings",
  DASHBOARD_SETTINGS_PROFILE: "/dashboard/settings/profile",
  DASHBOARD_SETTINGS_NOTIFICATIONS: "/dashboard/settings/notifications",
  DASHBOARD_SETTINGS_BILLING: "/dashboard/settings/billing",
  DASHBOARD_SETTINGS_MEMBERS: "/dashboard/settings/members",

  // Scyra Analytics sub-routes (if needed for specific scyra-only views)
  DASHBOARD_ANALYTICS_SCYRA_CONVERSATION_QUALITY:
    "/dashboard/analytics/scyra/conversation-quality",
  DASHBOARD_ANALYTICS_SCYRA_LIVE_MESSAGE_FEED:
    "/dashboard/analytics/scyra/live-message-feed",
  DASHBOARD_ANALYTICS_SCYRA_AGENT_ACTIVITY_LOG:
    "/dashboard/analytics/scyra/agent-activity-log",
  DASHBOARD_ANALYTICS_SCYRA_AT_RISK_ACCOUNTS:
    "/dashboard/analytics/scyra/at-risk-accounts",
  DASHBOARD_ANALYTICS_SCYRA_NEXT_ACTIONS:
    "/dashboard/analytics/scyra/next-actions",

  // Seto Analytics sub-routes
  DASHBOARD_ANALYTICS_SETO_ACTIVE_SETTLEMENTS:
    "/dashboard/analytics/seto/active-settlements",
  DASHBOARD_ANALYTICS_SETO_POLICY_THRESHOLD:
    "/dashboard/analytics/seto/policy-threshold",
  DASHBOARD_ANALYTICS_SETO_INTELLIGENCE_SNAPSHOT:
    "/dashboard/analytics/seto/intelligence-snapshot",
  DASHBOARD_ANALYTICS_SETO_EXCEPTION_QUEUE:
    "/dashboard/analytics/seto/exception-queue",
  DASHBOARD_ANALYTICS_SETO_HANDOFF_LOG: "/dashboard/analytics/seto/handoff-log",

  // Liora Analytics sub-routes
  DASHBOARD_ANALYTICS_LIORA_AGENT_INTERACTIONS:
    "/dashboard/analytics/liora/agent-interactions",
  DASHBOARD_ANALYTICS_LIORA_DOCUMENT_AUTOMATION:
    "/dashboard/analytics/liora/document-automation",
  DASHBOARD_ANALYTICS_LIORA_PIPELINE_TRACKER:
    "/dashboard/analytics/liora/pipeline-tracker",
  DASHBOARD_ANALYTICS_LIORA_ISSUE_EXCEPTION:
    "/dashboard/analytics/liora/issue-exception",
  DASHBOARD_ANALYTICS_LIORA_TEMPLATE_MANAGEMENT:
    "/dashboard/analytics/liora/template-management",
} as const;

// Route paths for React Router (without leading slash for children routes)
export const ROUTE_PATHS = {
  // Base app routes
  HOME: '',
  AGENTS: 'agents/:agentId',
  SIGNUP: 'signup',
  LOGIN: 'login',
  REDIRECT_HANDLER: 'redirect-handler',

  // Dashboard routes (for use in children arrays)
  DASHBOARD_AI_AGENTS: 'ai-agents',
  DASHBOARD_AGENT_SUITE: 'ai-agents/suite/:suiteId',
  DASHBOARD_AGENT_ACTIVATION: 'ai-agents/activate',
  DASHBOARD_AGENT_ACTIVATION_SUITE: 'ai-agents/activate/suite/:suiteId',
  DASHBOARD_AGENT_ACTIVATION_AGENT: 'ai-agents/activate/agent/:agentId',

  // Business Stack routes
  DASHBOARD_BUSINESS_STACK: 'business-stack',
  DASHBOARD_BUSINESS_STACK_SELECT_AGENT: 'business-stack/select-agent',
  DASHBOARD_BUSINESS_STACK_ACTIVATE_SUITE:
    'business-stack/activate-suite/:suiteId',
  DASHBOARD_BUSINESS_STACK_WITH_APP: 'business-stack/:appKey',

  // Knowledge Base routes
  DASHBOARD_KNOWLEDGE_BASE: 'knowledge-base',
  DASHBOARD_KNOWLEDGE_BASE_SELECT_AGENT: 'knowledge-base/select-agent',
  DASHBOARD_KNOWLEDGE_BASE_ACTIVATE_SUITE:
    'knowledge-base/activate-suite/:suiteId',

  // Knowledge Base routes (for use in children arrays)
  KNOWLEDGE_BASE_CATEGORY: 'knowledge-base/:categoryId',
  KNOWLEDGE_BASE_ITEM: 'knowledge-base/:categoryId/:itemId',

  // Analytics routes (for use in children arrays)
  DASHBOARD_ANALYTICS_BASE: 'analytics',
  DASHBOARD_SELECT_AGENT: 'analytics/select-agent',
  DASHBOARD_ANALYTICS_ACTIVATE_SUITE: 'analytics/activate-suite/:suiteId',
  DASHBOARD_ANALYTICS_SCYRA: 'analytics/scyra',
  
  // New analytics structure
  DASHBOARD_ANALYTICS_INSIGHTS: 'analytics/insights',
  DASHBOARD_ANALYTICS_INSIGHTS_ALL: 'analytics/insights/all',
  DASHBOARD_ANALYTICS_TASK_LOGS: 'analytics/task-logs',
  DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS: 'analytics/assignment-logs',
  
  // Legacy setiq routes (for backwards compatibility)
  DASHBOARD_ANALYTICS_SETIQ_DASHBOARD: 'analytics/setiq',
  DASHBOARD_ANALYTICS_SETIQ_COLLECTOR_PERFORMANCE:
    'analytics/setiq/collector-performance',
  DASHBOARD_ANALYTICS_SETIQ_DAILY_INSIGHTS: 'analytics/setiq/daily-insights',
  
  DASHBOARD_ANALYTICS_SETO: 'analytics/seto',
  DASHBOARD_ANALYTICS_LIORA: 'analytics/liora',

  // Settings routes (for use in children arrays)
  DASHBOARD_SETTINGS: 'settings',
  DASHBOARD_SETTINGS_PROFILE: 'settings/profile',
  DASHBOARD_SETTINGS_NOTIFICATIONS: 'settings/notifications',
  DASHBOARD_SETTINGS_BILLING: 'settings/billing',
  DASHBOARD_SETTINGS_MEMBERS: 'settings/members',

  // Scyra Browse all sections (nested under scyra analytics)
  SCYRA_LIVE_MESSAGE_FEED: 'live-message-feed',
  SCYRA_AT_RISK_ACCOUNTS: 'at-risk-accounts',
  SCYRA_CONVERSATION_QUALITY: 'conversation-quality',
  SCYRA_NEXT_ACTIONS: 'next-actions',
  SCYRA_AGENT_ACTIVITY_LOG: 'agent-activity-log',

  // Seto Browse all sections (nested under seto analytics)
  SETO_ACTIVE_SETTLEMENTS: 'active-settlements',
  SETO_POLICY_THRESHOLD: 'policy-threshold',
  SETO_INTELLIGENCE_SNAPSHOT: 'intelligence-snapshot',
  SETO_EXCEPTION_QUEUE: 'exception-queue',
  SETO_HANDOFF_LOG: 'handoff-log',

  // Liora Browse all sections (nested under liora analytics)
  LIORA_AGENT_INTERACTIONS: 'agent-interactions',
  LIORA_DOCUMENT_AUTOMATION: 'document-automation',
  LIORA_PIPELINE_TRACKER: 'pipeline-tracker',
  LIORA_ISSUE_EXCEPTION: 'issue-exception',
  LIORA_TEMPLATE_MANAGEMENT: 'template-management',
} as const;
