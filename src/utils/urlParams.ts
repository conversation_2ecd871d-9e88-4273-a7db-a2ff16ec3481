import { useSearchParams, useNavigate } from 'react-router-dom';

interface AnalyticsFilters {
  suite?: string;
  agent?: string;
}

export const useAnalyticsParams = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  const filters: AnalyticsFilters = {
    suite: searchParams.get('suite') || undefined,
    agent: searchParams.get('agent') || undefined,
  };

  const updateFilters = (newFilters: Partial<AnalyticsFilters>) => {
    const current = new URLSearchParams(searchParams);
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        current.set(key, value);
      } else {
        current.delete(key);
      }
    });

    setSearchParams(current);
  };

  const navigateWithFilters = (path: string) => {
    const queryString = searchParams.toString();
    navigate(`${path}${queryString ? `?${queryString}` : ''}`);
  };

  return {
    filters,
    updateFilters,
    navigateWithFilters,
  };
};

export const getUrlWithParams = (basePath: string, params: Record<string, string>) => {
  const url = new URL(basePath, window.location.origin);
  Object.entries(params).forEach(([key, value]) => {
    if (value) url.searchParams.set(key, value);
  });
  return url.pathname + url.search;
};