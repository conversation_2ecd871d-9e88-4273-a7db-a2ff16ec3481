import { useKeycloak } from '@react-keycloak/web';
import { useGetUserApi } from '../services/upivotalAgenticService';
import { useQuery } from '@tanstack/react-query';
import { GET_USER_QUERY } from '@/utils/queryKeys';
import { UserBasicInfoPayload } from '../types/user';
import { isDevEnvironment } from '@/utils/helpers';

export const useGetUser = <T extends UserBasicInfoPayload>(options = {}) => {
  const { initialized, keycloak } = useKeycloak();
  const getUser = useGetUserApi();
  const isDev = isDevEnvironment();

  return useQuery([GET_USER_QUERY], () => getUser<T>(), {
    ...options,
    select: (data) => {
      const {
        userInfo: { dateOfBirth, ...userRest },
        ...others
      } = data;

      const newData = {
        ...others,
        userInfo: {
          ...userRest,
          dateOfBirth: dateOfBirth && new Date(dateOfBirth),
        },
      };
      return newData;
    },
    enabled: isDev ? true : !!initialized && !!keycloak.authenticated,
  });
};
