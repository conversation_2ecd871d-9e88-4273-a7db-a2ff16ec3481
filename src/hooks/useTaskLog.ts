import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import taskLogService from '@/services/taskLogService';
import { CreateTaskLogRequest, UpdateTaskLogRequest } from '@/types/taskLog';

// Query keys for React Query cache management
export const TASK_LOG_QUERY_KEYS = {
  all: ['task-logs'] as const,
  lists: () => [...TASK_LOG_QUERY_KEYS.all, 'list'] as const,
  list: (tenantId: string) =>
    [...TASK_LOG_QUERY_KEYS.lists(), tenantId] as const,
  details: () => [...TASK_LOG_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...TASK_LOG_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook to fetch a single task log by ID
 */
export const useTaskLogDetails = (id: string, enabled = true) => {
  return useQuery({
    queryKey: TASK_LOG_QUERY_KEYS.detail(id),
    queryFn: () => taskLogService.getTaskLog(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => data.data, // Extract the TaskLog from the response
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook to fetch all task logs for a tenant
 */
export const useTaskLogsList = (tenantId: string, enabled = true) => {
  return useQuery({
    queryKey: TASK_LOG_QUERY_KEYS.list(tenantId),
    queryFn: () => taskLogService.getTaskLogsByTenant(tenantId),
    enabled: enabled && !!tenantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => data.data, // Extract the TaskLog[] from the response
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook to create a new task log
 */
export const useCreateTaskLogMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateTaskLogRequest) =>
      taskLogService.createTaskLog(payload),
    onSuccess: (data) => {
      // Invalidate and refetch task logs list for the tenant
      queryClient.invalidateQueries({
        queryKey: TASK_LOG_QUERY_KEYS.list(data.data.tenantId),
      });

      // Optionally add the new task log to the cache
      queryClient.setQueryData(TASK_LOG_QUERY_KEYS.detail(data.data.id), data);
    },
    onError: (error) => {
      console.error('Failed to create task log:', error);
    },
  });
};

/**
 * Hook to update a task log
 */
export const useUpdateTaskLogMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateTaskLogRequest) =>
      taskLogService.updateTaskLog(payload.id, payload),
    onSuccess: (data, variables) => {
      // Update the specific task log in cache
      queryClient.setQueryData(TASK_LOG_QUERY_KEYS.detail(variables.id), data);

      // Invalidate the list to ensure consistency
      queryClient.invalidateQueries({
        queryKey: TASK_LOG_QUERY_KEYS.list(data.data.tenantId),
      });
    },
    onError: (error) => {
      console.error('Failed to update task log:', error);
    },
  });
};

/**
 * Hook to delete a task log
 */
export const useDeleteTaskLogMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => taskLogService.deleteTaskLog(id),
    onSuccess: (_, deletedId) => {
      // Remove the task log from cache
      queryClient.removeQueries({
        queryKey: TASK_LOG_QUERY_KEYS.detail(deletedId),
      });

      // Invalidate all task log lists to update counts
      queryClient.invalidateQueries({
        queryKey: TASK_LOG_QUERY_KEYS.lists(),
      });
    },
    onError: (error) => {
      console.error('Failed to delete task log:', error);
    },
  });
};

/**
 * Utility hook to prefetch task log details
 */
export const usePrefetchTaskLog = () => {
  const queryClient = useQueryClient();

  return (id: string) => {
    queryClient.prefetchQuery({
      queryKey: TASK_LOG_QUERY_KEYS.detail(id),
      queryFn: () => taskLogService.getTaskLog(id),
      staleTime: 5 * 60 * 1000,
    });
  };
};