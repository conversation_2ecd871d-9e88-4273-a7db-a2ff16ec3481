import React from 'react';
import AnimatedModal from '../common/AnimatedModal';
import { useTenant } from '@/context/TenantContext';
import { AvailableApp } from '../../types/businessStack';

interface PreConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  app: AvailableApp | null;
}

const PreConnectionModal: React.FC<PreConnectionModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  app,
}) => {
  const { activeAgent } = useTenant();

  const capitalizeAgentName = (key: string) => {
    if (!key) return 'Agent';
    return key.charAt(0).toUpperCase() + key.slice(1);
  };

  const getAgentEmail = (agentKey: string) => {
    if (!agentKey) return '<EMAIL>';
    return `${agentKey.toLowerCase()}.<EMAIL>`;
  };

  if (!app) return null;

  const agentName = capitalizeAgentName(activeAgent || '');
  const agentEmail = getAgentEmail(activeAgent || '');

  return (
    <AnimatedModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Connect ${agentName} to ${app.name}`}
      maxWidth="md"
    >
      <div className="space-y-6">
        {/* App Info */}
        <div className="flex gap-4 justify-center items-center">
          <div className="flex-shrink-0 p-2 w-20 h-20 bg-gray-100 rounded-lg">
            <img
              src={app.logo}
              alt={`${app.name} logo`}
              className="object-contain w-full h-full rounded-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src =
                  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMjAgMTBWMzBNMTAgMjBIMzAiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+';
              }}
            />
          </div>
        </div>

        {/* Connection Message */}
        <div className="p-4 bg-orange-50 rounded-lg">
          <div className="flex gap-3 items-start">
            <div className="flex-1">
              <p className="text-sm text-gray-800">
                Connect {agentName} using the email you assigned to {agentName}{' '}
                at your company, e.g.{' '}
                <code className="rounded bg-gray-200 px-1 py-0.5 font-mono text-xs font-semibold">
                  {agentEmail}
                </code>
              </p>
            </div>
          </div>
        </div>

        {/* Important Note */}
        <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex gap-3 items-start">
            <div className="flex flex-shrink-0 justify-center items-center w-5 h-5">
              <svg
                className="w-4 h-4 text-yellow-600"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm text-yellow-800">
                <strong>Important:</strong> You are connecting the agent (not
                yourself) to this application. Make sure you use the agent's
                designated email address for authentication.
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 justify-center pt-4">
          <button
            onClick={onConfirm}
            className="px-6 py-2 text-sm font-medium text-white rounded-lg bg-primary hover:bg-orange-15 focus:outline-none focus:ring-2 focus:ring-primary/20"
          >
            Connect the agent (not you)
          </button>
        </div>
      </div>
    </AnimatedModal>
  );
};

export default PreConnectionModal;
