import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router-dom';
import classNames from 'classnames';
import { Icons } from '../../assets/icons/DashboardIcons';
import { ROUTES } from '../../constants/routes';
import { Button } from '@/components/ui/Button';

const DashboardSidebar: React.FC<{
  isCollapsed: boolean;
  toggleSidebar: () => void;
}> = ({ isCollapsed, toggleSidebar }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  const isSettingsPage = location.pathname.includes('/settings');

  const defaultSidebarItems = [
    {
      path: ROUTES.DASHBOARD_AI_AGENTS,
      label: 'AI Agents',
      icon: Icons.Agent,
    },
    {
      path: ROUTES.DASHBOARD_SELECT_AGENT,
      label: 'Dashboard',
      icon: Icons.Dashboard,
    },
    {
      path: ROUTES.DASHBOARD_KNOWLEDGE_BASE,
      label: 'Knowledge Base',
      icon: Icons.Knowledge,
    },
    {
      path: ROUTES.DASHBOARD_BUSINESS_STACK,
      label: 'Business Stack',
      icon: Icons.Stack,
    },
  ];

  const settingsSidebarItems = [
    {
      path: ROUTES.DASHBOARD_SETTINGS_PROFILE,
      label: 'My Profile',
      icon: Icons.Group,
    },
    // {
    //   path: ROUTES.DASHBOARD_SETTINGS_NOTIFICATIONS,
    //   label: 'Notifications',
    //   icon: Icons.Notification,
    // },
    {
      path: ROUTES.DASHBOARD_SETTINGS_BILLING,
      label: 'Billing and Usage',
      icon: Icons.BillingInfo,
    },
    // {
    //   path: ROUTES.DASHBOARD_SETTINGS_MEMBERS,
    //   label: 'Members',
    //   icon: Icons.Users,
    // },
  ];

  const sidebarItems = isSettingsPage
    ? settingsSidebarItems
    : defaultSidebarItems;

  const handleNavigation = (path: string) => {
    // Always redirect to selection pages for Knowledge Base and Business Stack
    if (path === ROUTES.DASHBOARD_KNOWLEDGE_BASE) {
      navigate(ROUTES.DASHBOARD_KNOWLEDGE_BASE_SELECT_AGENT);
      return;
    }
    if (path === ROUTES.DASHBOARD_BUSINESS_STACK) {
      navigate(ROUTES.DASHBOARD_BUSINESS_STACK_SELECT_AGENT);
      return;
    }

    navigate(path);
  };

  // Helper function to check if current path is a child of the parent route
  const isChildRoute = (parentPath: string, currentPath: string): boolean => {
    // Remove trailing slash for consistent comparison
    const cleanParentPath = parentPath.replace(/\/$/, '');
    const cleanCurrentPath = currentPath.replace(/\/$/, '');

    // Check if current path starts with parent path and has additional segments
    return (
      cleanCurrentPath.startsWith(cleanParentPath) &&
      cleanCurrentPath.length > cleanParentPath.length &&
      cleanCurrentPath[cleanParentPath.length] === '/'
    );
  };

  const isActive = (path: string) => {
    const currentPath = location.pathname;

    // For AI Agents - match exact path and all child routes
    if (path === ROUTES.DASHBOARD_AI_AGENTS) {
      return (
        currentPath === path ||
        isChildRoute(path, currentPath) ||
        // Handle specific AI agent child routes
        currentPath.includes('/ai-agents/suite/') ||
        currentPath.includes('/ai-agents/activate/')
      );
    }

    // For Analytics/Dashboard - match any analytics child route (setiq, seto, liora)
    if (path === ROUTES.DASHBOARD_SELECT_AGENT) {
      return (
        currentPath === path ||
        currentPath.startsWith(ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD) ||
        currentPath.startsWith(ROUTES.DASHBOARD_ANALYTICS_SETO) ||
        currentPath.startsWith(ROUTES.DASHBOARD_ANALYTICS_LIORA) ||
        // Handle any other analytics child routes
        currentPath.includes('/analytics/')
      );
    }

    // For Knowledge Base - match exact path, select-agent page, and all child routes
    if (path === ROUTES.DASHBOARD_KNOWLEDGE_BASE) {
      return (
        currentPath === path ||
        currentPath === ROUTES.DASHBOARD_KNOWLEDGE_BASE_SELECT_AGENT ||
        isChildRoute(path, currentPath) ||
        // Handle knowledge base category and item pages
        currentPath.includes('/knowledge-base/')
      );
    }

    // For Business Stack - match exact path, select-agent page, and all child routes
    if (path === ROUTES.DASHBOARD_BUSINESS_STACK) {
      return (
        currentPath === path ||
        currentPath === ROUTES.DASHBOARD_BUSINESS_STACK_SELECT_AGENT ||
        isChildRoute(path, currentPath) ||
        // Handle business stack with app parameter
        currentPath.includes('/business-stack/')
      );
    }

    // For settings routes, match exact path or default to profile if on base settings
    if (path === ROUTES.DASHBOARD_SETTINGS_PROFILE) {
      return (
        currentPath === path ||
        currentPath === ROUTES.DASHBOARD_SETTINGS ||
        currentPath.endsWith('/settings') ||
        isChildRoute(ROUTES.DASHBOARD_SETTINGS, currentPath)
      );
    }

    // For other routes, match exact path
    return currentPath === path;
  };

  return (
    <div className="relative">
      {/* Base collapsed sidebar that maintains layout */}
      <motion.div
        initial={false}
        animate={{ width: isCollapsed ? 68 : 300 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={classNames(
          'flex h-full flex-col',
          isCollapsed ? 'bg-[#FFFDF9]' : 'bg-white'
        )}
      >
        {/* Sidebar Content */}
        <div className="overflow-hidden flex-1 mt-4">
          {isCollapsed && !isHovered ? (
            // Collapsed State - Icons Only
            <div className="flex flex-col gap-4 h-full">
              <div className="h-full flex-1 space-y-4 p-2.5">
                {/* Navigation Items */}
                {sidebarItems.map((item, index) => (
                  <motion.button
                    key={item.label}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: (index + 1) * 0.1 }}
                    onClick={() => handleNavigation(item.path)}
                    className={`flex h-[50px] w-full items-center justify-center rounded-2xl transition-colors ${
                      isActive(item.path)
                        ? 'bg-[#FFECE3] text-[#FF3E00]'
                        : 'text-subText hover:bg-[#FFECE3]/80'
                    }`}
                    title={item.label}
                    onMouseEnter={() => setIsHovered(true)}
                  >
                    <item.icon
                      className={classNames(
                        'h-5 w-5',
                        isActive(item.path) ? 'text-primary' : 'text-blackOne'
                      )}
                    />
                  </motion.button>
                ))}
              </div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="p-2 border-t border-gray-200"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleSidebar}
                  className={classNames(
                    'h-[50px] w-full items-center justify-center rounded-2xl p-0 hover:bg-[#FFECE3] hover:text-[#FF3E00]'
                  )}
                >
                  {isCollapsed ? (
                    <Icons.SidebarOpen className="w-6 h-6" />
                  ) : (
                    <Icons.SidebarClose className="w-6 h-6" />
                  )}
                </Button>
              </motion.div>
            </div>
          ) : !isCollapsed ? (
            // Expanded State - Full Navigation
            <div className="flex flex-col gap-4 p-4">
              <motion.nav
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="space-y-2"
              >
                {sidebarItems.map((item, index) => (
                  <motion.button
                    key={item.label}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                    onClick={() => handleNavigation(item.path)}
                    className={`flex w-full items-center gap-3 rounded-2xl p-4 text-left transition-colors ${
                      isActive(item.path)
                        ? 'bg-[#FFECE3] text-[#FF3E00]'
                        : 'text-subText hover:bg-[#FFECE3]/80'
                    }`}
                  >
                    <item.icon
                      className={classNames(
                        'h-5 w-5',
                        isActive(item.path) ? 'text-primary' : 'text-blackOne'
                      )}
                    />
                    <span className="text-sm font-medium text-subText">
                      {item.label}
                    </span>
                  </motion.button>
                ))}
              </motion.nav>
            </div>
          ) : null}
        </div>

        {/* Bottom Section - Only in Expanded State */}
        {!isCollapsed && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="p-4 space-y-4 border-t border-gray-200"
          >
            {/* Close Sidebar Button */}
            <button
              onClick={toggleSidebar}
              className="flex gap-4 justify-start items-center p-3 w-full text-sm font-medium text-gray-600 bg-gray-100 rounded-xl transition-colors hover:bg-gray-200"
              title="Collapse Sidebar"
            >
              <Icons.SidebarClose className="w-5 h-5" />
              <span className="text-sm font-medium text-gray-600">
                Collapse Sidebar
              </span>
            </button>
            <div className="flex gap-4 items-center text-xs font-medium text-subText sm:text-base">
              <Icons.Wallet className="w-6 h-6" />
              Free Plan (Trial)
            </div>

            <div className="flex flex-col gap-2 font-medium">
              <div className="flex justify-between items-center w-full text-x text-subText sm:text-sm">
                <span>Activities:</span>
                <span>0 / 1,000</span>
              </div>
              <div className="mb-2 w-full h-2 bg-gray-200 rounded-full">
                <div
                  className="h-full rounded-full bg-primary"
                  style={{ width: '0%' }}
                />
              </div>
              <span className="text-x text-subText sm:text-sm">
                Trial ends on December 08, 2025
              </span>
            </div>
            <button className="p-4 w-full text-sm font-medium bg-white rounded-lg border transition-colors border-primary text-primary hover:bg-primary/90 hover:text-white">
              Upgrade
            </button>
          </motion.div>
        )}
      </motion.div>

      {/* Overlay expanded sidebar when hovering in collapsed state */}
      {isCollapsed && isHovered && (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
          className="absolute left-0 top-0 z-50 h-full w-[300px] border-r border-gray-200 bg-white shadow-2xl"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Overlay Sidebar Content */}
          <div className="overflow-hidden flex-1 mt-4">
            <div className="flex flex-col gap-4 p-4">
              <motion.nav
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="space-y-2"
              >
                {sidebarItems.map((item, index) => (
                  <motion.button
                    key={item.label}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 + index * 0.1 }}
                    onClick={() => handleNavigation(item.path)}
                    className={`flex w-full items-center gap-3 rounded-2xl p-4 text-left transition-colors ${
                      isActive(item.path)
                        ? 'bg-[#FFECE3] text-[#FF3E00]'
                        : 'text-subText hover:bg-[#FFECE3]/80'
                    }`}
                  >
                    <item.icon
                      className={classNames(
                        'h-5 w-5',
                        isActive(item.path) ? 'text-primary' : 'text-blackOne'
                      )}
                    />
                    <span className="text-sm font-medium text-subText">
                      {item.label}
                    </span>
                  </motion.button>
                ))}
              </motion.nav>
            </div>
          </div>

          {/* Bottom Section - Only in Expanded State */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="p-4 space-y-4 border-t border-gray-200"
          >
            <div className="flex gap-4 items-center text-xs font-medium text-subText sm:text-base">
              <Icons.Wallet className="w-6 h-6" />
              Free Plan (Trial)
            </div>

            <div className="flex flex-col gap-2 font-medium">
              <div className="flex justify-between items-center w-full text-x text-subText sm:text-sm">
                <span>Activities:</span>
                <span>0 / 1,000</span>
              </div>
              <div className="mb-2 w-full h-2 bg-gray-200 rounded-full">
                <div
                  className="h-full rounded-full bg-primary"
                  style={{ width: '0%' }}
                />
              </div>
              <span className="text-x text-subText sm:text-sm">
                Trial ends on December 08, 2025
              </span>
            </div>
            <button className="p-4 w-full text-sm font-medium bg-white rounded-lg border transition-colors border-primary text-primary hover:bg-primary/90 hover:text-white">
              Upgrade
            </button>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default DashboardSidebar;
