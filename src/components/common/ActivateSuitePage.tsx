import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import { ROUTES } from '@/constants/routes';
import { agentSuites as mockAgentsSuite } from '@/data/constants';
import {
  AIAgent,
  useClaimAgentSuiteApi,
} from '@/services/upivotalAgenticService';
import { useGetAIAgentSuites } from '@/hooks/useAIAgents';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { useTenant } from '@/context/TenantContext';
import { Spinner } from '@/components/common/Loader';
import { AgentCard } from '@/pages/AiAgentsPage';
import { claimIcon } from '@/assets/icons';

export type PageType = 'ai-agents' | 'business-stack' | 'analytics' | 'knowledge-base';

interface ActivateSuitePageProps {
  pageType: PageType;
  suiteId: string;
}

const ActivateSuitePage: React.FC<ActivateSuitePageProps> = ({
  pageType,
  suiteId,
}) => {
  const location = useLocation();
  const navigate = useNavigate();

  // Primary data flow: API-based approach
  const { data: agentSuites = [], isLoading: isLoadingSuites } = useGetAIAgentSuites();
  const suite = agentSuites.find(s => s.agentSuiteKey === suiteId);

  // Fallback: location.state for backward compatibility
  const fallbackSuite = location.state;
  
  // Use API data first, fallback to location.state if needed
  const finalSuite = suite || fallbackSuite;

  // Get agents that belong to this suite
  const suiteAgents: AIAgent[] = finalSuite?.availableAgents || [];
  const suiteFallbackImage = mockAgentsSuite.filter(
    mockAgent =>
      mockAgent.id.toLowerCase() === finalSuite?.agentSuiteKey.toLowerCase(),
  )[0]?.image;

  const [isLoading, setIsLoading] = useState(false);
  const [chatMessage, setChatMessage] = useState<string>('');
  const { activeAgent, setActiveAgent, tenantId, setTenantId } = useTenant();

  const claimAgentsSuite = useClaimAgentSuiteApi();

  // Check if the current agents suite is already claimed (simplified for single tenant)
  const isAgentSuiteClaimed = !!tenantId;

  const handleAgentSelect = (agentKey: string) => {
    setActiveAgent(agentKey);
  };

  // Dynamic navigation based on pageType
  const getSuccessRoute = () => {
    switch (pageType) {
      case 'ai-agents':
        return ROUTES.DASHBOARD_AGENT_ACTIVATION_SUITE(finalSuite.id);
      case 'business-stack':
        return ROUTES.DASHBOARD_BUSINESS_STACK;
      case 'analytics':
        return ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD;
      case 'knowledge-base':
        return ROUTES.DASHBOARD_KNOWLEDGE_BASE;
      default:
        return ROUTES.DASHBOARD_BASE;
    }
  };

  // Dynamic breadcrumb configuration
  const getBreadcrumbConfig = () => {
    switch (pageType) {
      case 'ai-agents':
        return {
          text: 'Agents Suite',
          action: () => navigate(ROUTES.DASHBOARD_AI_AGENTS),
        };
      case 'knowledge-base':
        return {
          text: 'Knowledge Base',
          action: () => navigate(-1),
        };
      default:
        return {
          text: 'Agents Suite',
          action: () => navigate(-1),
        };
    }
  };

  const handleClaimAgentSuite = async (agentSuiteKey: string) => {
    try {
      setIsLoading(true);
      setChatMessage('');

      const response = await claimAgentsSuite(agentSuiteKey);

      if (response.status === true) {
        // Set the tenant ID from the response
        setTenantId(response.data.tenantId);

        // Display success message in chat interface
        setChatMessage(response.message || "Agents suite claimed successfully!");

        navigate(getSuccessRoute());
      } else {
        // Display error message in chat interface
        setChatMessage(response.message || "Failed to claim agents suite");
      }
    } catch (error: unknown) {
      const errorMessage =
        typeof error === 'object' && error !== null && 'response' in error
          ? (error as any).response?.data?.message
          : error instanceof Error
            ? error.message
            : 'Failed to claim agents suite. Please try again.';

      setChatMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  useEffect(() => {
    if (!activeAgent) return;

    const reloadForAgent = async () => {
      try {
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }
      } catch (error) {
        console.error('Error occured while changing agent:', error);
      }
    };

    reloadForAgent();
  }, [activeAgent]);

  // Show loading state while fetching data
  if (isLoadingSuites && !fallbackSuite) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <Spinner className="mx-auto mb-4 w-8 h-8" />
          <p className="text-lg text-gray-600">Loading agents suite...</p>
        </div>
      </div>
    );
  }

  // Show error state if suite not found
  if (!finalSuite) {
    const breadcrumbConfig = getBreadcrumbConfig();
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Agents Suite Not Found
          </h1>
          <p className="mb-4 text-gray-600">
            The agents suite with ID "{suiteId}" could not be found.
          </p>
          <button
            onClick={breadcrumbConfig.action}
            className="px-6 py-3 font-medium text-white rounded-lg transition-colors bg-primary hover:bg-orange-15"
          >
            {pageType === 'ai-agents' ? 'Back to Agents Hub' : 'Go Back'}
          </button>
        </div>
      </div>
    );
  }

  const breadcrumbConfig = getBreadcrumbConfig();

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalMessage={chatMessage}
      />

      {/* Main Content - RIGHT SIDE */}
      <div className="overflow-y-auto flex-1">
        <div className="flex flex-col gap-y-4 p-8">
          {/* Breadcrumb */}
          {pageType === "ai-agents" ? (
            <Link
              to={ROUTES.DASHBOARD_AI_AGENTS}
              className="flex gap-1 items-center font-semibold text-blackTwo"
            >
              <ChevronLeft className="w-4 h-4 sm:h-6 sm:w-6" strokeWidth={2} />
              {breadcrumbConfig.text}
            </Link>
          ) : (
            <button
              onClick={breadcrumbConfig.action}
              className="flex gap-1 items-center font-semibold text-blackTwo"
            >
              <ChevronLeft className="w-4 h-4 sm:h-6 sm:w-6" strokeWidth={2} />
              {breadcrumbConfig.text}
            </button>
          )}

          {/* Suite Header */}
          <div className="max-w-[732px] font-spartan">
            <div className="relative h-[198px] overflow-hidden rounded-lg border bg-gray-200 bg-cover bg-center">
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{
                  backgroundImage: finalSuite.avatar
                    ? `url(${finalSuite.avatar})`
                    : `url(${suiteFallbackImage})`,
                }}
              />
              {/* Dark Overlay */}
              <div className="absolute inset-0 bg-black/20" />
              {/* Content */}
              <div className="relative z-10 flex h-full flex-col justify-center p-6">
                <h1 className="w-fit rounded bg-white px-4 py-2 text-[32px] font-bold backdrop-blur-sm">
                  {finalSuite.agentSuiteName}
                </h1>

                <h2 className="mt-8 w-fit rounded text-[20px] font-semibold text-white">
                  {finalSuite.description}
                </h2>
                <div className="font-inter text-lg text-white">
                  {finalSuite.roleDescription}
                </div>
              </div>
            </div>

            {!isAgentSuiteClaimed && (
              <div className="mb-4 mt-8 flex items-center justify-between rounded-lg border border-primary bg-peachTwo p-6">
                <div className="flex items-center gap-2">
                  <img
                    src={claimIcon}
                    alt=""
                    className="h-[46px] w-[46px] object-cover"
                  />
                  <div>
                    <div className="font-medium text-blackOne">
                      Claim Your Suite
                    </div>
                    <p className="mt-1 font-inter text-sm text-subText">
                      Start using your AI agents immediately by claiming this
                      suite
                    </p>
                  </div>
                </div>
                <button
                  className="rounded bg-primary px-4 py-3 font-normal text-white transition-colors hover:bg-orange-15 disabled:opacity-30"
                  onClick={() =>
                    handleClaimAgentSuite(finalSuite.agentSuiteKey)
                  }
                  disabled={isLoading}
                >
                  <div className="flex items-center gap-2">
                    {isLoading && (
                      <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                    )}
                    <span>Claim this suite</span>
                  </div>
                </button>
              </div>
            )}
          </div>

          {/* Agents Grid */}
          {suiteAgents.length > 0 && (
            <div className="grid grid-cols-1 gap-6 w-fit md:grid-cols-2 lg:grid-cols-2">
              {suiteAgents.map((agent) => (
                <AgentCard
                  key={agent.agentKey}
                  agent={agent}
                  showChatButton={true}
                  isActiveAgent={activeAgent === agent.agentKey}
                  link={
                    agent.agentKey
                      ? ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(agent.agentKey)
                      : "#"
                  }
                  onAgentSelect={() => handleAgentSelect(agent.agentKey)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActivateSuitePage;