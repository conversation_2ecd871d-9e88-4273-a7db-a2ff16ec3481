import React, { useState } from 'react';
import { ChevronUp, ChevronDown, MoreHorizontal } from 'lucide-react';
import clsx from 'clsx';

export interface Column<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: T[keyof T], row: T) => React.ReactNode;
  className?: string;
}

export interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  className?: string;
  onRowClick?: (row: T) => void;
  sortable?: boolean;
  loading?: boolean;
  emptyMessage?: string;
  rowColoring?: boolean;
  rowColoringType?: 'even' | 'odd';
  rowColoringColorClass?: string;
}

type SortDirection = 'asc' | 'desc' | null;

function DataTable<T extends Record<string, any>>({
  data,
  columns,
  className = '',
  onRowClick,
  sortable = false,
  loading = false,
  emptyMessage = 'No data available',
  rowColoring = true,
  rowColoringType = 'odd',
  rowColoringColorClass = 'bg-[#FFFAF7]',
}: DataTableProps<T>) {
  const [sortColumn, setSortColumn] = useState<keyof T | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  const handleSort = (columnKey: keyof T) => {
    if (!sortable) return;

    const column = columns.find((col) => col.key === columnKey);
    if (!column?.sortable) return;

    if (sortColumn === columnKey) {
      // Cycle through: asc -> desc -> null
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortDirection(null);
        setSortColumn(null);
      } else {
        setSortDirection('asc');
      }
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
  };

  const getSortedData = () => {
    if (!sortColumn || !sortDirection) return data;

    return [...data].sort((a, b) => {
      const aVal = a[sortColumn];
      const bVal = b[sortColumn];

      if (aVal === null || aVal === undefined) return 1;
      if (bVal === null || bVal === undefined) return -1;

      let comparison = 0;
      if (aVal < bVal) comparison = -1;
      if (aVal > bVal) comparison = 1;

      return sortDirection === 'desc' ? -comparison : comparison;
    });
  };

  const renderSortIcon = (columnKey: keyof T) => {
    const column = columns.find((col) => col.key === columnKey);
    if (!sortable || !column?.sortable) return null;

    if (sortColumn === columnKey) {
      return sortDirection === 'asc' ? (
        <ChevronUp className="w-4 h-4" />
      ) : (
        <ChevronDown className="w-4 h-4" />
      );
    }

    return <ChevronDown className="w-4 h-4 text-gray-300" />;
  };

  const sortedData = getSortedData();

  if (loading) {
    return (
      <div className={clsx('overflow-hidden bg-white rounded-xl', className)}>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            {/* Header */}
            <thead>
              <tr>
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    className={clsx(
                      'px-4 py-5 text-left text-xs sm:text-sm font-medium sm:font-semibold text-grayTen capitalize tracking-wider',
                      column.className
                    )}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.label}</span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            {/* Body with loading animation */}
            <tbody className="bg-white">
              {Array.from({ length: 10 }).map((_, index) => (
                <tr>
                  {[...Array(9)].map((_, index) => (
                    <td key={index} className="px-4 py-5">
                      <div className="h-4 bg-gray-100 rounded-full"></div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('overflow-hidden bg-white rounded-xl', className)}>
      <div className="overflow-x-auto pb-10">
        <table className="min-w-full">
          {/* Header */}
          <thead>
            <tr>
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  onClick={() => handleSort(column.key)}
                  className={clsx(
                    'px-4 py-5 text-left text-xs sm:text-sm font-medium sm:font-semibold text-grayTen capitalize tracking-wider',
                    sortable &&
                      column.sortable &&
                      'cursor-pointer hover:bg-gray-100',
                    column.className
                  )}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.label}</span>
                    {renderSortIcon(column.key)}
                  </div>
                </th>
              ))}
            </tr>
          </thead>

          {/* Body */}
          <tbody className="bg-white">
            {sortedData.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + 1}
                  className="px-6 py-8 text-center text-subText"
                >
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              sortedData.map((row, index) => (
                <tr
                  key={index}
                  onClick={() => onRowClick?.(row)}
                  className={clsx(
                    'transition-all ease-in-out duration-200 border-white hover:border hover:border-primary',
                    onRowClick && 'cursor-pointer',
                    rowColoring &&
                      (rowColoringType === 'even'
                        ? index % 2 !== 0
                        : index % 2 === 0) &&
                      rowColoringColorClass
                  )}
                >
                  {columns.map((column) => (
                    <td
                      key={String(column.key)}
                      className={clsx(
                        'p-4 whitespace-nowrap text-sm text-subText',
                        column.className
                      )}
                    >
                      {column.render
                        ? column.render(row[column.key], row)
                        : String(row[column.key] || '')}
                    </td>
                  ))}
                  <td className="px-4 py-5 text-sm font-medium text-right whitespace-nowrap">
                    <button className="text-subText">
                      <MoreHorizontal className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default DataTable;