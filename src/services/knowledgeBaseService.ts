import { usePivotlPrivateRequest } from '@/lib/axios/usePrivateRequest';
import { BASE_URL } from '@/utils/apiUrls';
import { agenticService, agenticUserService } from '@/utils/apiServiceControllersRoute';
import { useTenant } from '@/context/TenantContext';

// TypeScript interfaces for Knowledge Base API
export interface KnowledgeBaseUploadRequest {
  filePart: File;
  fileTag: string;
}

export interface KnowledgeBaseDeleteRequest {
  fileTag: string;
}

export interface KnowledgeBaseResponse {
  status: boolean;
  message: string;
  data?: any;
}

export interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
  uploadedAt?: string;
}

// Suite Level Knowledge Base API Hooks
export const useSuiteKnowledgeBaseApi = () => {
  const { tenantId, claimedSuites } = useTenant();
  
  // For suite level, we need X-Active-Tenant and X-Active-Agent-Suite headers
  const getSuiteAxiosInstance = (agentSuiteKey: string) => {
    return usePivotlPrivateRequest(BASE_URL, tenantId || '', '');
  };

  const checkSuiteKnowledgeBase = async (agentSuiteKey: string): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getSuiteAxiosInstance(agentSuiteKey);
      const response = await axiosInstance.current?.get(
        `${agenticService}/ai-agent-suites/knowledge-base`,
        {
          headers: {
            'X-Active-Agent-Suite': agentSuiteKey,
          },
        }
      );
      return response?.data || { status: false, message: 'No data' };
    } catch (error) {
      console.error('Suite Knowledge Base Check API Error:', error);
      throw new Error('Failed to check suite knowledge base. Please try again.');
    }
  };

  const uploadSuiteKnowledgeBase = async (
    agentSuiteKey: string,
    payload: KnowledgeBaseUploadRequest
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getSuiteAxiosInstance(agentSuiteKey);
      const formData = new FormData();
      formData.append('filePart', payload.filePart);
      formData.append('fileTag', payload.fileTag);

      const response = await axiosInstance.current?.post(
        `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-Active-Agent-Suite': agentSuiteKey,
          },
        }
      );
      return response?.data || { status: false, message: 'Upload failed' };
    } catch (error) {
      console.error('Suite Knowledge Base Upload API Error:', error);
      throw new Error('Failed to upload suite knowledge base document. Please try again.');
    }
  };

  const replaceSuiteKnowledgeBase = async (
    agentSuiteKey: string,
    payload: KnowledgeBaseUploadRequest
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getSuiteAxiosInstance(agentSuiteKey);
      const formData = new FormData();
      formData.append('filePart', payload.filePart);
      formData.append('fileTag', payload.fileTag);

      const response = await axiosInstance.current?.put(
        `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-Active-Agent-Suite': agentSuiteKey,
          },
        }
      );
      return response?.data || { status: false, message: 'Replace failed' };
    } catch (error) {
      console.error('Suite Knowledge Base Replace API Error:', error);
      throw new Error('Failed to replace suite knowledge base document. Please try again.');
    }
  };

  const deleteSuiteKnowledgeBase = async (
    agentSuiteKey: string,
    payload: KnowledgeBaseDeleteRequest
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getSuiteAxiosInstance(agentSuiteKey);
      const response = await axiosInstance.current?.delete(
        `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        {
          data: payload,
          headers: {
            'X-Active-Agent-Suite': agentSuiteKey,
          },
        }
      );
      return response?.data || { status: false, message: 'Delete failed' };
    } catch (error) {
      console.error('Suite Knowledge Base Delete API Error:', error);
      throw new Error('Failed to delete suite knowledge base document. Please try again.');
    }
  };

  return {
    checkSuiteKnowledgeBase,
    uploadSuiteKnowledgeBase,
    replaceSuiteKnowledgeBase,
    deleteSuiteKnowledgeBase,
  };
};

// Agent Level Knowledge Base API Hooks
export const useAgentKnowledgeBaseApi = () => {
  const { tenantId, activeAgent } = useTenant();
  
  // For agent level, we need X-Active-Tenant and X-Active-Agent headers
  const getAgentAxiosInstance = (agentKey?: string) => {
    return usePivotlPrivateRequest(BASE_URL, tenantId || '', agentKey || activeAgent || '');
  };

  const checkAgentKnowledgeBase = async (agentKey?: string): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getAgentAxiosInstance(agentKey);
      const response = await axiosInstance.current?.get(
        `${agenticService}/ai-agents/knowledge-base`
      );
      return response?.data || { status: false, message: 'No data' };
    } catch (error) {
      console.error('Agent Knowledge Base Check API Error:', error);
      throw new Error('Failed to check agent knowledge base. Please try again.');
    }
  };

  const uploadAgentKnowledgeBase = async (
    payload: KnowledgeBaseUploadRequest,
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getAgentAxiosInstance(agentKey);
      const formData = new FormData();
      formData.append('filePart', payload.filePart);
      formData.append('fileTag', payload.fileTag);

      const response = await axiosInstance.current?.post(
        `${agenticService}/ai-agents/knowledge-base-upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response?.data || { status: false, message: 'Upload failed' };
    } catch (error) {
      console.error('Agent Knowledge Base Upload API Error:', error);
      throw new Error('Failed to upload agent knowledge base document. Please try again.');
    }
  };

  const replaceAgentKnowledgeBase = async (
    payload: KnowledgeBaseUploadRequest,
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getAgentAxiosInstance(agentKey);
      const formData = new FormData();
      formData.append('filePart', payload.filePart);
      formData.append('fileTag', payload.fileTag);

      const response = await axiosInstance.current?.put(
        `${agenticService}/ai-agents/knowledge-base-upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response?.data || { status: false, message: 'Replace failed' };
    } catch (error) {
      console.error('Agent Knowledge Base Replace API Error:', error);
      throw new Error('Failed to replace agent knowledge base document. Please try again.');
    }
  };

  const deleteAgentKnowledgeBase = async (
    payload: KnowledgeBaseDeleteRequest,
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getAgentAxiosInstance(agentKey);
      const response = await axiosInstance.current?.delete(
        `${agenticService}/ai-agents/knowledge-base-upload`,
        {
          data: payload,
        }
      );
      return response?.data || { status: false, message: 'Delete failed' };
    } catch (error) {
      console.error('Agent Knowledge Base Delete API Error:', error);
      throw new Error('Failed to delete agent knowledge base document. Please try again.');
    }
  };

  return {
    checkAgentKnowledgeBase,
    uploadAgentKnowledgeBase,
    replaceAgentKnowledgeBase,
    deleteAgentKnowledgeBase,
  };
};
