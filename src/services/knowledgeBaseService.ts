import { usePivotlPrivateRequest } from "@/lib/axios/usePrivateRequest";
import { BASE_URL } from "@/utils/apiUrls";
import { agenticService } from "@/utils/apiServiceControllersRoute";
import { useTenant } from "@/context/TenantContext";

// TypeScript interfaces for Knowledge Base API
export interface KnowledgeBaseUploadRequest {
  filePart: File;
  fileTag: string;
}

export interface KnowledgeBaseDeleteRequest {
  fileTag: string;
}

export interface KnowledgeBaseResponse {
  status: boolean;
  message: string;
  data?: any;
}

export interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
  uploadedAt?: string;
}

// Suite Level Knowledge Base API Hooks
export const useSuiteKnowledgeBaseApi = () => {
  const { tenantId } = useTenant();
  // Initialize axios instance ref via hook at the top level of this custom hook
  const getSuiteAxiosInstance = usePivotlPrivateRequest(
    BASE_URL,
    tenantId || "",
    ""
  );

  const checkSuiteKnowledgeBase = async (
    agentSuiteKey: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getSuiteAxiosInstance;

      if (!axiosInstance?.current) {
        throw new Error("Axios instance not initialized");
      }

      console.log("Making Suite Knowledge Base Check API call:", {
        endpoint: `${agenticService}/ai-agent-suites/knowledge-base`,
        agentSuiteKey,
        tenantId,
      });

      const response = await axiosInstance.current.get(
        `${agenticService}/ai-agent-suites/knowledge-base`,
        {
          headers: {
            // Ensure suite header is provided per-request
            "X-Active-Agent-Suite": agentSuiteKey,
          },
        }
      );

      console.log("Suite Knowledge Base Check API response:", response);
      return response?.data || { status: false, message: "No data" };
    } catch (error) {
      console.error("Suite Knowledge Base Check API Error:", error);
      throw new Error(
        "Failed to check suite knowledge base. Please try again."
      );
    }
  };

  const uploadSuiteKnowledgeBase = async (
    agentSuiteKey: string,
    payload: KnowledgeBaseUploadRequest
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getSuiteAxiosInstance;

      if (!axiosInstance?.current) {
        throw new Error("Axios instance not initialized");
      }

      const formData = new FormData();
      formData.append("filePart", payload.filePart);
      formData.append("fileTag", payload.fileTag);

      console.log("Making Suite Knowledge Base Upload API call:", {
        endpoint: `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        agentSuiteKey,
        tenantId,
        fileTag: payload.fileTag,
        fileName: payload.filePart.name,
      });

      const response = await axiosInstance.current.post(
        `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            // Provide suite header per-request rather than relying on axios default
            "X-Active-Agent-Suite": agentSuiteKey,
          },
        }
      );

      console.log("Suite Knowledge Base Upload API response:", response);
      return response?.data || { status: false, message: "Upload failed" };
    } catch (error) {
      console.error("Suite Knowledge Base Upload API Error:", error);
      throw new Error(
        "Failed to upload suite knowledge base document. Please try again."
      );
    }
  };

  const replaceSuiteKnowledgeBase = async (
    agentSuiteKey: string,
    payload: KnowledgeBaseUploadRequest
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getSuiteAxiosInstance;

      if (!axiosInstance?.current) {
        throw new Error("Axios instance not initialized");
      }

      const formData = new FormData();
      formData.append("filePart", payload.filePart);
      formData.append("fileTag", payload.fileTag);

      console.log("Making Suite Knowledge Base Replace API call:", {
        endpoint: `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        agentSuiteKey,
        tenantId,
        fileTag: payload.fileTag,
        fileName: payload.filePart.name,
      });

      const response = await axiosInstance.current.put(
        `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            "X-Active-Agent-Suite": agentSuiteKey,
          },
        }
      );

      console.log("Suite Knowledge Base Replace API response:", response);
      return response?.data || { status: false, message: "Replace failed" };
    } catch (error) {
      console.error("Suite Knowledge Base Replace API Error:", error);
      throw new Error(
        "Failed to replace suite knowledge base document. Please try again."
      );
    }
  };

  const deleteSuiteKnowledgeBase = async (
    agentSuiteKey: string,
    payload: KnowledgeBaseDeleteRequest
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getSuiteAxiosInstance;

      if (!axiosInstance?.current) {
        throw new Error("Axios instance not initialized");
      }

      console.log("Making Suite Knowledge Base Delete API call:", {
        endpoint: `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        agentSuiteKey,
        tenantId,
        fileTag: payload.fileTag,
      });

      const response = await axiosInstance.current.delete(
        `${agenticService}/ai-agent-suites/knowledge-base-upload`,
        {
          data: payload,
          headers: {
            "X-Active-Agent-Suite": agentSuiteKey,
          },
        }
      );

      console.log("Suite Knowledge Base Delete API response:", response);
      return response?.data || { status: false, message: "Delete failed" };
    } catch (error) {
      console.error("Suite Knowledge Base Delete API Error:", error);
      throw new Error(
        "Failed to delete suite knowledge base document. Please try again."
      );
    }
  };

  return {
    checkSuiteKnowledgeBase,
    uploadSuiteKnowledgeBase,
    replaceSuiteKnowledgeBase,
    deleteSuiteKnowledgeBase,
  };
};

// Agent Level Knowledge Base API Hooks
export const useAgentKnowledgeBaseApi = () => {
  const { tenantId, activeAgent } = useTenant();

  // Initialize axios instance ref via hook at top-level.
  const getAgentAxiosInstance = usePivotlPrivateRequest(
    BASE_URL,
    tenantId || "",
    // pass empty default agent; per-request header will override if agentKey provided
    activeAgent || ""
  );

  const checkAgentKnowledgeBase = async (
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getAgentAxiosInstance;

      if (!axiosInstance?.current) {
        throw new Error("Axios instance not initialized");
      }

      console.log("Making Agent Knowledge Base Check API call:", {
        endpoint: `${agenticService}/ai-agents/knowledge-base`,
        agentKey: agentKey || activeAgent,
        tenantId,
      });

      const response = await axiosInstance.current.get(
        `${agenticService}/ai-agents/knowledge-base`,
        {
          headers: {
            // Ensure active agent header is present per-request
            "X-Active-Agent": agentKey || activeAgent,
          },
        }
      );

      console.log("Agent Knowledge Base Check API response:", response);
      return response?.data || { status: false, message: "No data" };
    } catch (error) {
      console.error("Agent Knowledge Base Check API Error:", error);
      throw new Error(
        "Failed to check agent knowledge base. Please try again."
      );
    }
  };

  const uploadAgentKnowledgeBase = async (
    payload: KnowledgeBaseUploadRequest,
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getAgentAxiosInstance;

      if (!axiosInstance?.current) {
        throw new Error("Axios instance not initialized");
      }

      const formData = new FormData();
      formData.append("filePart", payload.filePart);
      formData.append("fileTag", payload.fileTag);

      console.log("Making Agent Knowledge Base Upload API call:", {
        endpoint: `${agenticService}/ai-agents/knowledge-base-upload`,
        agentKey: agentKey || activeAgent,
        tenantId,
        fileTag: payload.fileTag,
        fileName: payload.filePart.name,
      });

      const response = await axiosInstance.current.post(
        `${agenticService}/ai-agents/knowledge-base-upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            "X-Active-Agent": agentKey || activeAgent,
          },
        }
      );

      console.log("Agent Knowledge Base Upload API response:", response);
      return response?.data || { status: false, message: "Upload failed" };
    } catch (error) {
      console.error("Agent Knowledge Base Upload API Error:", error);
      throw new Error(
        "Failed to upload agent knowledge base document. Please try again."
      );
    }
  };

  const replaceAgentKnowledgeBase = async (
    payload: KnowledgeBaseUploadRequest,
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getAgentAxiosInstance;

      if (!axiosInstance?.current) {
        throw new Error("Axios instance not initialized");
      }

      const formData = new FormData();
      formData.append("filePart", payload.filePart);
      formData.append("fileTag", payload.fileTag);

      console.log("Making Agent Knowledge Base Replace API call:", {
        endpoint: `${agenticService}/ai-agents/knowledge-base-upload`,
        agentKey: agentKey || activeAgent,
        tenantId,
        fileTag: payload.fileTag,
        fileName: payload.filePart.name,
      });

      const response = await axiosInstance.current.put(
        `${agenticService}/ai-agents/knowledge-base-upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            "X-Active-Agent": agentKey || activeAgent,
          },
        }
      );

      console.log("Agent Knowledge Base Replace API response:", response);
      return response?.data || { status: false, message: "Replace failed" };
    } catch (error) {
      console.error("Agent Knowledge Base Replace API Error:", error);
      throw new Error(
        "Failed to replace agent knowledge base document. Please try again."
      );
    }
  };

  const deleteAgentKnowledgeBase = async (
    payload: KnowledgeBaseDeleteRequest,
    agentKey?: string
  ): Promise<KnowledgeBaseResponse> => {
    try {
      const axiosInstance = getAgentAxiosInstance;

      if (!axiosInstance?.current) {
        throw new Error("Axios instance not initialized");
      }

      console.log("Making Agent Knowledge Base Delete API call:", {
        endpoint: `${agenticService}/ai-agents/knowledge-base-upload`,
        agentKey: agentKey || activeAgent,
        tenantId,
        fileTag: payload.fileTag,
      });

      const response = await axiosInstance.current.delete(
        `${agenticService}/ai-agents/knowledge-base-upload`,
        {
          data: payload,
          headers: {
            "X-Active-Agent": agentKey || activeAgent,
          },
        }
      );

      console.log("Agent Knowledge Base Delete API response:", response);
      return response?.data || { status: false, message: "Delete failed" };
    } catch (error) {
      console.error("Agent Knowledge Base Delete API Error:", error);
      throw new Error(
        "Failed to delete agent knowledge base document. Please try again."
      );
    }
  };

  return {
    checkAgentKnowledgeBase,
    uploadAgentKnowledgeBase,
    replaceAgentKnowledgeBase,
    deleteAgentKnowledgeBase,
  };
};
