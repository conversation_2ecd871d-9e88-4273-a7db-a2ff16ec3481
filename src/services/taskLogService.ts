import { axiosInstance } from '@/helpers/axiosConfig';
import { agenticService } from '@/utils/apiServiceControllersRoute';
import {
  CreateTaskLogRequest,
  UpdateTaskLogRequest,
  TaskLogResponse,
  TaskLogListResponse,
  DeleteTaskLogResponse,
} from '@/types/taskLog';

class TaskLogService {
  private static instance: TaskLogService;
  private readonly BASE_URL: string;

  private constructor() {
    this.BASE_URL = `${agenticService}/task-logs`;
  }

  public static getInstance(): TaskLogService {
    if (!TaskLogService.instance) {
      TaskLogService.instance = new TaskLogService();
    }
    return TaskLogService.instance;
  }

  async createTaskLog(payload: CreateTaskLogRequest): Promise<TaskLogResponse> {
    const response = await axiosInstance.post(this.BASE_URL, payload);
    return response.data;
  }

  async getTaskLog(id: string): Promise<TaskLogResponse> {
    const response = await axiosInstance.get(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  async getTaskLogsByTenant(tenantId: string): Promise<TaskLogListResponse> {
    const response = await axiosInstance.get(`${this.BASE_URL}/tenant/${tenantId}`);
    return response.data;
  }

  async updateTaskLog(id: string, payload: UpdateTaskLogRequest): Promise<TaskLogResponse> {
    const response = await axiosInstance.put(`${this.BASE_URL}/${id}`, payload);
    return response.data;
  }

  async deleteTaskLog(id: string): Promise<DeleteTaskLogResponse> {
    const response = await axiosInstance.delete(`${this.BASE_URL}/${id}`);
    return response.data;
  }
}

export default TaskLogService.getInstance();
