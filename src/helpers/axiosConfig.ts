import { isDevEnvironment } from '@/utils/helpers';
import { BASE_URL } from '@/utils/apiUrls';
import type { AxiosInstance } from 'axios';
import axios from 'axios';

const getKeycloakToken = (): string | undefined => {
  return import.meta.env.VITE_DEV_ACCESS_TOKEN;
};

const createAxiosInstance = (): AxiosInstance => {
  const token = getKeycloakToken();
  
  const instance = axios.create({
    baseURL: BASE_URL,
    headers: {
      Authorization: isDevEnvironment() ? `Bearer ${token}` : undefined,
    },
    timeout: 120000,
    maxContentLength: Infinity,
    maxBodyLength: Infinity,
  });

  instance.defaults.headers.common['Save-Data'] = 'on';

  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error.code === 'ECONNABORTED') {
        console.warn('Request timed out. Retrying...');
        return instance?.request(error.config);
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

export const axiosInstance = createAxiosInstance();