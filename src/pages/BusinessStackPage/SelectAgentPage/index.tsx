import React from 'react';
import AgentSelectionLayout from '../../../components/layout/AgentSelectionLayout';
import { useTenant } from '../../../context/TenantContext';
import { ROUTES } from '../../../constants/routes';
import { Navigate, useNavigate } from 'react-router-dom';
import { businessStackBg } from '@/assets/images';
import { useGetUser } from '@/hooks/useUser';

const BusinessStackSelectAgentPage: React.FC = () => {
  const navigate = useNavigate();
    const { data: userData, isLoading } = useGetUser();
  
  // Check if user has claimed the specific agents suite
  const isAgentSuiteClaimed = (suiteKey: string) => {
    return userData?.userInfo?.tenant?.claimedAgentSuites?.some(
      (claimedSuite) => claimedSuite.suite.agentSuiteKey === suiteKey
    );
  };

  return (
    <AgentSelectionLayout
      title="Seamless System Connections"
      description="Link agents to your CRM, communication tools, and workflows with secure per-agent authentication."
      bgImage={businessStackBg}
      pageType="business-stack"
      onAgentSuiteClick={suite => {
        if (!isAgentSuiteClaimed(suite.agentSuiteKey)) {
          navigate(
            ROUTES.DASHBOARD_BUSINESS_STACK_ACTIVATE_SUITE(suite.agentSuiteKey),
          );
        } else {
          navigate(ROUTES.DASHBOARD_BUSINESS_STACK);
        }
      }}
    />
  );
};

export default BusinessStackSelectAgentPage;
