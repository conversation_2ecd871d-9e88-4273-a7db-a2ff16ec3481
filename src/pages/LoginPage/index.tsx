import { useState, useMemo } from 'react';
import { useKeycloak } from '@react-keycloak/web';
import { useStreamingRegisChat } from '../../hooks/useStreamingRegisChat';
import { RegisChatInterface } from '../../components/chat/RegisChatInterface';
import { ChatInput } from '../../components/chat/ChatInput';
import { eclipse, halfEclipse } from '../../assets/images';
import { Link } from 'react-router-dom';
import { ROUTES } from '../../constants/routes';
import { Eye, EyeOff } from 'lucide-react';
import Logo from '@/components/ui/Logo';

const LoginFormSidebar = ({ state, sendMessage }: any) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const { keycloak } = useKeycloak();

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) return;

    setIsLoggingIn(true);
    try {
      // Redirect to a redirect handler that will check tenant count and redirect accordingly
      const redirectUri = window.location.origin + '/pivotl/redirect-handler';

      await keycloak?.login({
        redirectUri,
      });
    } catch (error) {
      console.error('Error initiating Keycloak login:', error);
      // Fallback: redirect to main login page or show error
      window.location.href = '/login';
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleForgotPassword = () => {
    sendMessage('I have forgotten my password');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleLogin();
    }
  };

  return (
    <div
      className="flex overflow-hidden relative flex-col justify-center p-6 bg-gradient-to-br to-orange-100 rounded from-orange-50/50 font-inter"
      style={{ height: '100vh' }}
    >
      {/* Background Objects */}
      <div
        className="pointer-events-none absolute inset-0 z-0 -mt-48 bg-[right] bg-no-repeat"
        style={{
          backgroundImage: `url(${halfEclipse})`,
          backgroundSize: 'auto',
        }}
      />
      <div
        className="pointer-events-none absolute inset-0 z-0 bg-[right_bottom] bg-no-repeat"
        style={{
          backgroundImage: `url(${eclipse})`,
          backgroundSize: 'auto',
        }}
      />

      <div className="overflow-y-auto relative z-10 p-6 max-h-full bg-white rounded-2xl shadow-sm">
        <div className="space-y-4">
          {/* Email Input */}
          <div>
            <input
              type="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              onKeyDown={handleKeyDown}
              className="px-3 py-2 w-full h-12 text-sm rounded-md border border-gray-300 transition-all text-blackTwo focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
              placeholder="Email address"
              disabled={state.isLoading || isLoggingIn}
            />
          </div>

          {/* Password Input */}
          <div>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={e => setPassword(e.target.value)}
                onKeyDown={handleKeyDown}
                className="px-3 py-2 pr-10 mt-4 w-full h-12 text-sm rounded-md border border-gray-300 transition-all text-blackTwo focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
                placeholder="Password"
                disabled={state.isLoading || isLoggingIn}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 text-gray-400 -translate-y-1/2 hover:text-primary"
                disabled={state.isLoading || isLoggingIn}
              >
                {showPassword ? (
                  <EyeOff className="mt-4 w-5 h-5" />
                ) : (
                  <Eye className="mt-4 w-5 h-5" />
                )}
              </button>
            </div>
          </div>

          {/* Forgot Password Link */}
          <button
            onClick={handleForgotPassword}
            className="text-sm transition-colors text-primary hover:text-primary/80"
            disabled={state.isLoading || isLoggingIn}
          >
            Forgot Password?
          </button>

          <div className="py-2">
            <hr className="border-primary" />
          </div>

          {/* Login Button */}
          <button
            onClick={handleLogin}
            disabled={
              !email.trim() ||
              !password.trim() ||
              state.isLoading ||
              isLoggingIn
            }
            className="px-6 py-2 w-full font-medium rounded-md border transition-colors border-primary bg-lightOrangeTwo text-blue-midnight hover:border-lightOrangeTwo hover:bg-orange-15 hover:text-white disabled:cursor-not-allowed disabled:border-none disabled:bg-gray-400 disabled:text-whiteOff disabled:opacity-50"
          >
            {isLoggingIn ? 'Logging in...' : 'Login'}
          </button>

          {/* Signup Link */}
          <div className="text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link
                to={ROUTES.SIGNUP}
                className="font-medium transition-colors text-primary hover:text-primary/80"
              >
                Sign up here
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export const PivotlLoginPage = () => {
  const { state, sendMessage, isStreaming, streamingMessage } =
    useStreamingRegisChat('login');

  // Memoize the chat input component to prevent recreation on every render
  const ChatInputComponent = useMemo(() => {
    return () => (
      <ChatInput
        onSendMessage={sendMessage}
        placeholder="Ask me anything about login or password recovery..."
        disabled={state.isLoading}
      />
    );
  }, [sendMessage, state.isLoading]);

  return (
    <div className="mx-auto max-w-screen-3xl font-inter">
      <div className="grid grid-cols-1 h-screen lg:grid-cols-3">
        {/* LHS - Chat Interface */}
        <div className="relative lg:col-span-2 lg:px-24">
          <div className="sticky top-0 z-20 pt-2 pb-6 bg-white">
            <Logo />
          </div>
          <div className="h-[calc(100vh-100px)] bg-white">
            <RegisChatInterface
              state={state}
              ChatInputComponent={ChatInputComponent}
              isStreaming={isStreaming}
              streamingMessage={streamingMessage}
            />
          </div>
        </div>

        {/* RHS - Login Form Sidebar */}
        <div className="lg:col-span-1">
          <LoginFormSidebar state={state} sendMessage={sendMessage} />
        </div>
      </div>
    </div>
  );
};

export default PivotlLoginPage;
