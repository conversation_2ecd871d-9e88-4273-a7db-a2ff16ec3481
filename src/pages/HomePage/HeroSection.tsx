import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { arrowPrimary, chatIcon, paperPlane } from "../../assets/icons";
import { fullEclipse, regis } from "../../assets/images";
import { ROUTES } from "../../constants/routes";
import { AuthProvider, useAuth } from "../../context/AuthContext";
import { TenantProvider, useTenant } from "../../context/TenantContext";
import { AIAgent } from "@/services/upivotalAgenticService";
import { useGetAIAgentSuites } from "@/hooks/useAIAgents";

const AgentPill = ({
  name,
  icon = regis,
  onClick,
}: {
  name: string;
  icon?: string;
  onClick?: () => void;
}) => (
  <div
    className="flex w-fit cursor-pointer items-center gap-2 rounded-lg border border-primary p-1.5 transition-all hover:scale-105"
    onClick={onClick}
  >
    <div className="h-7 w-7 flex-shrink-0">
      <img
        src={icon}
        alt={name}
        className="w-full h-full flex items-center justify-center rounded-full bg-[#718EBF33] object-cover"
      />
    </div>
    <span className="font-medium">{name}</span>
    <img src={chatIcon} alt="chat icon" />
  </div>
);

const AgentFlowIndicator = ({
  agents,
  onAgentClick,
  isPaused,
}: {
  agents: AIAgent[];
  onAgentClick: (agent: AIAgent) => void;
  isPaused: boolean;
}) => {
  if (agents.length === 0) return null;

  // Create duplicated agents for seamless infinite scroll
  const duplicatedAgents = [...agents, ...agents, ...agents];

  const renderAgentWithArrow = (
    agent: AIAgent,
    index: number,
    isLast: boolean
  ) => (
    <div
      key={`${agent.agentKey}-${index}`}
      className="flex flex-shrink-0 items-center py-2"
    >
      <AgentPill
        name={agent.agentName}
        icon={agent.avatar || regis}
        onClick={() => onAgentClick(agent)}
      />
      {!isLast && (
        <img src={arrowPrimary} alt="arrow" className="mx-4 flex-shrink-0" />
      )}
    </div>
  );

  return (
    <div className="relative mt-6 w-full md:w-[438px] overflow-hidden">
      {/* Gradient masks for smooth edges */}
      {/* Left edge */}
      <div className="pointer-events-none absolute left-0 top-0 z-10 h-full w-12 bg-gradient-to-r from-[#fff7f5] to-transparent" />
      {/* Right edge */}
      <div className="pointer-events-none absolute right-0 top-0 z-10 h-full w-12 bg-gradient-to-l from-[#fffefe] to-transparent" />

      <div
        className={`flex items-center gap-0 transition-all ${isPaused ? "[animation-play-state:paused]" : ""} animate-scroll-right`}
        style={{
          width: "max-content",
        }}
      >
        {duplicatedAgents.map((agent, index) =>
          renderAgentWithArrow(
            agent,
            index,
            index === duplicatedAgents.length - 1
          )
        )}
      </div>
    </div>
  );
};

const HeroSectionContent = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { data: agentSuites = [] } = useGetAIAgentSuites();

  // Combine all agents from all suites and add hardcoded Regis agent
  const allAgents: AIAgent[] = [
    ...agentSuites.flatMap((suite) => suite.availableAgents || []),
    {
      agentName: "Regis",
      agentKey: "regis",
      description: "Registration & Access Assistance",
      roleDescription: "I handle account setup and resolve access issues",
      avatar: regis,
      roles: ["Account setup, simplify login", "Resolve access issues"],
      categories: ["ACCESS_SERVICES"],
      enabled: true,
    },
  ];

  const { setActiveAgent } = useTenant();
  const [selectedAgent, setSelectedAgent] = useState<AIAgent>(
    allAgents.find((agent) => agent.agentKey === "regis") || allAgents[0]
  );
  const [isPaused, setIsPaused] = useState(false);
  const [userMessage, setUserMessage] = useState("");

  // Update selected agent when agents are loaded
  useEffect(() => {
    if (allAgents.length > 0 && !selectedAgent) {
      const regisAgent = allAgents.find((agent) => agent.agentKey === "regis");
      setSelectedAgent(regisAgent || allAgents[0]);
    }
  }, [allAgents, selectedAgent]);

  const handleAgentClick = (agent: AIAgent) => {
    setSelectedAgent(agent);
    setIsPaused(true);
    setActiveAgent(agent.agentKey);
    // Resume scrolling after 3 seconds
    setTimeout(() => setIsPaused(false), 3000);
  };

  const handleInputFocus = () => {
    setIsPaused(true);
  };

  const handleInputBlur = () => {
    setIsPaused(false);
  };

  const handleSendMessage = () => {
    if (!userMessage.trim()) return;

    if (selectedAgent?.agentKey === "regis") {
      navigate(ROUTES.SIGNUP);
    } else {
      // Find the suite that contains this agent
      const suite = agentSuites.find((s) =>
        s.availableAgents?.some((a) => a.agentKey === selectedAgent?.agentKey)
      );
      if (suite) {
        navigate(ROUTES.AGENTS(suite.agentSuiteKey), {
          state: { selectedAgent, userMessage },
        });
      }
    }
  };

  return (
    <section className="relative overflow-hidden">
      {/* Background Ball */}
      <div
        className="pointer-events-none absolute right-0 top-0 z-0 mt-12 h-16 w-16 md:h-[108px] md:w-[108px]"
        style={{
          backgroundImage: `url(${fullEclipse})`,
          backgroundSize: "contain",
          backgroundPosition: "right top",
          backgroundRepeat: "no-repeat",
          transform: "translate(15%, -30%)",
        }}
      />

      <div className="relative z-10 mx-auto flex max-w-screen-2xl flex-col items-center px-4 py-24 sm:px-6 md:flex-row md:gap-8 lg:px-8">
        {/* LHS - Content */}
        <div className="md:min-w-1/2 w-full text-center md:w-1/2 md:text-left">
          <h1 className="mb-6 text-4xl font-semibold leading-normal text-gray-900 md:text-[56px]">
            The Agentic AI
            <br />
            Transformation Hub
          </h1>
          <p className="mb-8 max-w-[627px] font-inter text-lg text-gray-700">
            Orchestrate, evolve, and deploy AI agents across your enterprise.
            <br />
            Agentous PivoTL is the transformation layer that deploys autonomous
            agentic teams to manage critical business functions and accelerate
            value creation.
          </p>
          <div className="flex flex-col justify-center gap-4 px-4 sm:flex-row md:justify-start md:px-0">
            <Link
              to={isAuthenticated ? ROUTES.DASHBOARD_BASE : ROUTES.SIGNUP}
              className="rounded-md bg-primary px-8 py-[9px] font-medium text-white transition-colors hover:bg-orange-15"
            >
              {isAuthenticated ? (
                "Go to Dashboard"
              ) : (
                <div className="flex items-center justify-center gap-3">
                  <svg
                    width="18"
                    height="18"
                    viewBox="0 0 18 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.25047 7.62487C6.25047 7.44254 6.32291 7.26767 6.45184 7.13874C6.58077 7.00981 6.75564 6.93737 6.93797 6.93737H11.063C11.2453 6.93737 11.4202 7.00981 11.5491 7.13874C11.678 7.26767 11.7505 7.44254 11.7505 7.62487C11.7505 7.80721 11.678 7.98208 11.5491 8.11101C11.4202 8.23994 11.2453 8.31237 11.063 8.31237H6.93797C6.75564 8.31237 6.58077 8.23994 6.45184 8.11101C6.32291 7.98208 6.25047 7.80721 6.25047 7.62487ZM6.93797 9.68737C6.75564 9.68737 6.58077 9.75981 6.45184 9.88874C6.32291 10.0177 6.25047 10.1925 6.25047 10.3749C6.25047 10.5572 6.32291 10.7321 6.45184 10.861C6.58077 10.9899 6.75564 11.0624 6.93797 11.0624H9.68797C9.87031 11.0624 10.0452 10.9899 10.1741 10.861C10.303 10.7321 10.3755 10.5572 10.3755 10.3749C10.3755 10.1925 10.303 10.0177 10.1741 9.88874C10.0452 9.75981 9.87031 9.68737 9.68797 9.68737H6.93797ZM0.750475 8.99987C0.750826 7.18356 1.35056 5.41813 2.45664 3.97745C3.56272 2.53677 5.11333 1.50136 6.86791 1.03186C8.62249 0.562356 10.483 0.684996 12.1607 1.38076C13.8385 2.07651 15.2398 3.3065 16.1472 4.87991C17.0546 6.45332 17.4174 8.2822 17.1793 10.0828C16.9412 11.8835 16.1156 13.5552 14.8305 14.8387C13.5453 16.1223 11.8725 16.9458 10.0716 17.1816C8.27067 17.4175 6.44225 17.0524 4.86997 16.143L1.65522 17.2155C1.53646 17.2552 1.40911 17.2616 1.28694 17.2342C1.16476 17.2068 1.0524 17.1465 0.961967 17.0599C0.871537 16.9733 0.806478 16.8636 0.77381 16.7427C0.741142 16.6219 0.742107 16.4944 0.7766 16.374L1.7556 12.9489C1.09563 11.7373 0.750045 10.3795 0.750475 8.99987ZM9.00047 2.12487C7.7866 2.1248 6.59434 2.44612 5.5449 3.05617C4.49546 3.66622 3.62625 4.54326 3.02562 5.59812C2.42499 6.65299 2.11437 7.84808 2.12532 9.0619C2.13627 10.2757 2.46841 11.465 3.08797 12.5089C3.13564 12.5895 3.16633 12.679 3.17815 12.772C3.18997 12.8649 3.18268 12.9592 3.15672 13.0492L2.45685 15.4967L4.7366 14.7377C4.83342 14.7054 4.93622 14.6951 5.03754 14.7075C5.13885 14.7198 5.23614 14.7546 5.32235 14.8092C6.22335 15.3795 7.24462 15.7323 8.30545 15.8398C9.36628 15.9474 10.4376 15.8067 11.4347 15.4289C12.4318 15.0511 13.3273 14.4466 14.0506 13.6631C14.7738 12.8796 15.3049 11.9386 15.6019 10.9146C15.8988 9.89048 15.9535 8.81137 15.7616 7.76251C15.5697 6.71365 15.1365 5.72381 14.4961 4.87122C13.8558 4.01863 13.0259 3.32667 12.0721 2.85003C11.1183 2.37338 10.0667 2.12512 9.00047 2.12487Z"
                      fill="white"
                    />
                  </svg>

                  <span className="mt-0.5">Chat with Regis</span>
                </div>
              )}
            </Link>
          </div>
        </div>

        {/* RHS - Interactive agents interface */}
        <div className="w-full md:w-1/2">
          <div className="flex items-center justify-center pt-10 font-inter md:pt-0">
            <div className="w-full md:w-[438px]">
              <div className="relative rounded-2xl bg-white px-4 py-6 shadow transition-all duration-300 hover:border hover:border-primary/20 hover:shadow-[0_0_20px_rgba(255,107,53,0.15)]">
                <div className="mb-6 flex gap-3">
                  {/* Selected agent's image */}
                  <div className="h-12 w-12 flex-shrink-0">
                    <div className="rounded-full bg-grayTwentySix">
                      <img
                        src={selectedAgent?.avatar || regis}
                        alt={selectedAgent?.agentName || "Agent"}
                        className="h-full w-full rounded-full object-cover"
                      />
                    </div>
                  </div>

                  <div className="flex-1">
                    <div className="mb-1 flex items-center gap-2">
                      {/* Selected agent's name */}
                      <span className="font-semibold text-darkGray">
                        {selectedAgent?.agentName || "Agent"}
                      </span>
                    </div>
                    <div className="rounded-lg bg-gray-5 p-3 font-medium text-grayTwentyFour">
                      {/* Selected agent's description */}
                      <div className="w-fit">
                        <span>
                          {selectedAgent?.description ||
                            "Hi, I'm ready to get started!"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  className="flex h-10 cursor-pointer items-center justify-between rounded-md border border-grayNine pl-2 pr-4 text-grayTen transition-colors hover:border-primary"
                  onClick={() => {
                    const input = document.querySelector(
                      'input[type="text"]'
                    ) as HTMLInputElement;
                    if (input) {
                      input.focus();
                      handleInputFocus();
                    }
                  }}
                >
                  <input
                    type="text"
                    value={userMessage}
                    onChange={(e) => setUserMessage(e.target.value)}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleSendMessage();
                      }
                    }}
                    className="flex-1 bg-transparent outline-none [border:none] [box-shadow:none] placeholder:text-grayTen focus:[border:none] focus:[box-shadow:none] active:[border:none]"
                    placeholder="I'm here — whenever you're ready."
                  />
                  <img
                    src={paperPlane}
                    alt="send message"
                    className="cursor-pointer transition-opacity hover:opacity-70"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSendMessage();
                    }}
                  />
                </div>
              </div>

              {/* Agents carousel */}
              <AgentFlowIndicator
                agents={allAgents}
                onAgentClick={handleAgentClick}
                isPaused={isPaused}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export const HeroSection = () => {
  return (
    <TenantProvider>
      <AuthProvider>
        <HeroSectionContent />
      </AuthProvider>
    </TenantProvider>
  );
};
