import { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { bgGradient, eclipseShadow } from "../../assets/images";
import { agentSuites as mockAgents } from "../../data/constants";
import { ROUTES } from "../../constants/routes";
import { AIAgent } from "../../services/upivotalAgenticService";
import AgentSuiteSkeletonLoader from "../../components/ui/AgentSuiteSkeleton";
import { useGetAIAgentSuites } from "../../hooks/useAIAgents";
import { useTenant } from "@/context/TenantContext";

interface TransformedAgentSuite {
  agentSuiteName: string;
  agentSuiteKey: string;
  agentKey?: string;
  description: string;
  roleDescription: string;
  avatar: string;
}

interface TransformedResponse {
  status: boolean;
  message: string;
  data: {
    aiAgentSuites: TransformedAgentSuite[];
    total: number;
    page: number;
    pageSize: number;
  };
}

function transformAgentSuites(response: any): TransformedResponse {
  // Extract the original suite and its available agents
  const originalSuite = response.data.aiAgentSuites[0];
  const availableAgents = originalSuite.availableAgents || [];

  // Create new suites from available agents
  const newSuites = availableAgents.map((agent: AIAgent) => ({
    agentSuiteName: agent.agentName,
    agentKey: agent.agentKey,
    agentSuiteKey: originalSuite.agentSuiteKey,
    description: agent.description,
    roleDescription: agent.roleDescription,
    avatar: agent.avatar,
  }));

  const allSuites = [originalSuite, ...newSuites];

  return {
    ...response,
    data: {
      ...response.data,
      aiAgentSuites: allSuites,
      total: allSuites.length, // Update total count
    },
  };
}

export const AgentTeamsSection = () => {
  const { setActiveAgent } = useTenant();
  // Use React Query hook for data fetching
  const { data: rawAgentSuites = [], isLoading } = useGetAIAgentSuites();

  // Transform the agent suites data using the existing transformation logic
  const agentSuite: TransformedAgentSuite[] =
    rawAgentSuites.length > 0
      ? transformAgentSuites({
          status: true,
          message: "",
          data: {
            aiAgentSuites: rawAgentSuites,
            total: rawAgentSuites.length,
            page: 1,
            pageSize: 20,
          },
        }).data.aiAgentSuites
      : [];

  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const scrollRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 1);
    }
  }, []);

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  const scroll = (direction: "left" | "right") => {
    if (scrollRef.current) {
      const scrollAmount = direction === "left" ? -300 : 300;
      scrollRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  };

  return (
    <section className="py-20 bg-white">
      {/* Gradient overlay */}
      <div
        className="absolute inset-0 z-0 pointer-events-none"
        style={{
          backgroundImage: `url(${bgGradient})`,
          backgroundSize: "auto",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      />

      <div className="container relative z-10 mx-auto -mt-20 max-w-screen-2xl px-6">
        <div
          className="pointer-events-none absolute left-[18%] top-3 z-0 h-72 w-72 bg-no-repeat"
          style={{
            backgroundImage: `url(${eclipseShadow})`,
            backgroundSize: "contain",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            // transform: 'translate(15%, -30%)',
          }}
        />
        <div className="text-center">
          <h2 className="mb-4 text-3xl font-bold text-center">
            Meet The AI Agent Teams
          </h2>
          <p className="mx-auto mb-12 text-lg text-center text-gray-600 font-inter">
            Each Agentous team is a suite of purpose-trained AI Agents designed
            to deliver and sustain key business functions.
          </p>
        </div>

        <div className="relative w-full">
          {/* Left Arrow */}
          {!isLoading && showLeftArrow && (
            <button
              onClick={() => scroll("left")}
              className="absolute left-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll left"
            >
              <ChevronLeft className="h-8 w-8" />
            </button>
          )}

          {/* AI Agents */}
          <div className="flex justify-center">
            <div
              ref={scrollRef}
              onScroll={handleScroll}
              className={`no-scrollbar flex snap-x snap-mandatory gap-6 overflow-x-auto scroll-smooth px-4 pb-6 pt-2 ${
                !isLoading &&
                agentSuite.length < 5 &&
                "justify-start lg:justify-center"
              }`}
            >
              {isLoading ? (
                <AgentSuiteSkeletonLoader count={4} />
              ) : (
                agentSuite.map((suite, index) => (
                  // <Link
                  //   to={
                  //     isAuthenticated
                  //       ? ROUTES.DASHBOARD_AGENT_SUITE(suite.agentSuiteKey)
                  //       : ROUTES.PIVOTL_AGENTS(suite.agentSuiteKey)
                  //   }
                  <Link
                    to={ROUTES.AGENTS(suite.agentSuiteKey)}
                    key={index}
                    className={`flex w-[290px] min-w-[290px] cursor-pointer flex-col items-center overflow-hidden rounded border transition-all hover:shadow-md`}
                  >
                    <div
                      onClick={() => {
                        if (suite.agentKey) {
                          setActiveAgent(suite.agentKey);
                        } else {
                          setActiveAgent("colton");
                        }
                      }}
                    >
                      <img
                        src={suite.avatar}
                        className="h-56 w-full bg-peachTwo object-contain"
                        alt={suite.agentSuiteName}
                        onError={(e) => {
                          const agentKey = suite.agentKey
                            ? suite.agentKey.toLowerCase()
                            : suite.agentSuiteKey.toLowerCase();

                          // Fallback to mock logo if agent avatar fails to load
                          (e.target as HTMLImageElement).src =
                            mockAgents.filter(
                              (agent) => agent.id.toLowerCase() === agentKey
                            )[0].image;
                        }}
                      />
                      <div className="flex flex-col gap-4 p-4 text-blackOne">
                        <div className="w-fit rounded border border-grayNine bg-grayNineTeen px-2 py-[2px] font-bold">
                          {suite.agentSuiteName}
                        </div>
                        <p className="text-lg font-semibold">
                          {suite.description}
                        </p>
                        <p className="mb-3 font-inter text-darkGray">
                          {suite.roleDescription}
                        </p>
                      </div>
                    </div>
                  </Link>
                ))
              )}
            </div>
          </div>

          {/* Right Arrow */}
          {!isLoading && showRightArrow && (
            <button
              onClick={() => scroll("right")}
              className="absolute right-2 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
              aria-label="Scroll right"
            >
              <ChevronRight className="h-8 w-8" />
            </button>
          )}
        </div>
      </div>
    </section>
  );
};
