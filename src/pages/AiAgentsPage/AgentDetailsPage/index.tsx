import { use<PERSON><PERSON><PERSON>, <PERSON>, useLocation } from "react-router-dom";
import { ChevronRight, ArrowDown, User } from "lucide-react";
import { useRef, useState, useEffect, useCallback } from "react";
import moment from "moment";
import ReactMarkdown from "react-markdown";
// import { useOnClickOutside } from '@/hooks/useOnClickOutside';
import { useScyraChatApi } from "@/services/scyraChatService";
import { ROUTES } from "@/constants/routes";
import { featureIcons, agentSuites as mockAgents } from "@/data/constants";
import { setIq, vesa } from "@/assets/images";
import { useGetAIAgentsData } from "@/hooks/useAIAgents";
import { useTenant } from "@/context/TenantContext";
import { AIAgent } from "@/services/upivotalAgenticService";
import { MainLoaderSkeleton } from "@/components/hocs/suspense/withSuspense";
import { ChatInput } from "@/components/chat/ChatInput";
import { TypingIndicator } from "@/components/chat/TypingIndicator";
import AgentSuiteSkeletonLoader from "@/components/ui/AgentSuiteSkeleton";
import { AgentCard } from "..";

interface ChatMessage {
  id: string;
  sender: string;
  content: string;
  timestamp: Date;
  senderName: string;
}

interface ChatState {
  messages: ChatMessage[];
  isLoading: boolean;
  sessionId: string;
}

const generateSecureSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

const MessageComponent = ({
  message,
  agentAvatar,
}: {
  message: ChatMessage;
  agentAvatar?: string;
}) => {
  const isUser = message.sender === "user";

  return (
    <div className="mb-6 flex gap-3">
      {/* Avatar */}
      <div className="h-10 w-10 flex-shrink-0">
        {isUser ? (
          <div className="flex h-full w-full items-center justify-center rounded-full bg-peachTwo">
            <User className="h-5 w-5 text-gray-600" />
          </div>
        ) : (
          <div className="rounded-full bg-grayTwentySix">
            <img
              src={agentAvatar || vesa}
              alt={message.senderName}
              className="h-full w-full rounded-full object-cover"
            />
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-darkGray">
            {message.senderName}
          </span>
          <span className="text-sm text-gray-400">
            {moment(message.timestamp).format("h:mm A")}
          </span>
        </div>

        {/* Message Text */}
        <div className="rounded-lg bg-gray-5 p-3 text-grayTwentyFour">
          <ReactMarkdown
            components={{
              p: (props) => <p className="mb-2 last:mb-0">{props.children}</p>,
              strong: (props) => (
                <strong className="font-semibold text-darkGray">
                  {props.children}
                </strong>
              ),
              em: (props) => (
                <em className="italic text-gray-600">{props.children}</em>
              ),
              code: (props) => (
                <code className="rounded bg-gray-100 px-1 py-0.5 font-mono text-sm text-gray-800">
                  {props.children}
                </code>
              ),
              pre: (props) => (
                <pre className="mt-2 overflow-x-auto rounded bg-gray-100 p-2 font-mono text-sm text-gray-800">
                  {props.children}
                </pre>
              ),
              ul: (props) => (
                <ul className="ml-4 list-disc space-y-1">{props.children}</ul>
              ),
              ol: (props) => (
                <ol className="ml-4 list-decimal space-y-1">
                  {props.children}
                </ol>
              ),
              li: (props) => (
                <li className="text-grayTwentyFour">{props.children}</li>
              ),
              a: (props) => (
                <a
                  href={props.href}
                  className="hover:text-primaryDark text-primary underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {props.children}
                </a>
              ),
            }}
          >
            {message.content}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
};

const AgentDetailsPage = () => {
  const location = useLocation();
  const chatWithAgent = useScyraChatApi();

  // Chat state management
  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    sessionId: generateSecureSessionId(),
  });

  // Scroll functionality
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);

  const { agents, agentSuites, isLoadingAgents, isLoadingSuites } =
    useGetAIAgentsData();
  const { activeAgent: tenantActiveAgent, setActiveAgent } = useTenant();

  const { agentId: agentSuiteId } = useParams<{ agentId: string }>();
  const agentSuite = agentSuiteId
    ? agentSuites.find(
        (agentSuite) => agentSuite.agentSuiteKey === agentSuiteId
      )
    : null;

  // Handle location state from HeroSection navigation
  const locationState = location.state as {
    selectedAgent?: AIAgent;
    userMessage?: string;
  } | null;

  // Track if we came from HeroSection with location state
  const [isFromHeroSection, setIsFromHeroSection] = useState(
    !!locationState?.selectedAgent
  );

  const activeAgent = (isFromHeroSection && locationState?.selectedAgent) ||
    agents.find((agent) => agent.agentKey === tenantActiveAgent) || {
      agentName: "Colton",
      agentKey: "colton",
      description: "Collections Coordination & Manager",
      roleDescription:
        "I ensure operations adhere to legal, ethical, and brand standards",
      avatar: vesa,
      roles: [
        "Provide guidance on regulatory matters",
        "Monitor all agent and user interactions",
        "Flag compliance risks",
      ],
      categories: ["COLLECTION_SERVICES"],
    };

  // Set active agent in context when coming from HeroSection
  useEffect(() => {
    if (locationState?.selectedAgent && isFromHeroSection) {
      setActiveAgent(locationState.selectedAgent.agentKey);
    }
  }, [locationState?.selectedAgent, setActiveAgent, isFromHeroSection]);

  // Track auto-send to prevent duplicates
  const hasAutoSentMessage = useRef(false);

  // Track current agent to detect switches
  const currentAgentRef = useRef(activeAgent.agentKey);
  const [isAgentSwitching, setIsAgentSwitching] = useState(false);

  const [showAgentFeatures, setShowAgentFeatures] = useState(true);
  const agentFeaturesRef = useRef<HTMLDivElement>(null);
  const toggleAgentFeatures = () =>
    setShowAgentFeatures((previous) => !previous);

  // useOnClickOutside(agentFeaturesRef, () => setShowAgentFeatures(false));

  // Scroll functionality
  const scrollToBottom = useCallback(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }, []);

  // Show/hide down-arrow when user scrolls up and track user position
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const atBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight <
        40;
      setShowScrollToBottom(!atBottom);
      setIsUserAtBottom(atBottom);
    };

    container.addEventListener("scroll", handleScroll);
    handleScroll();

    return () => container.removeEventListener("scroll", handleScroll);
  }, [chatState.messages.length]);

  // Auto-scroll to bottom only when user is already at bottom and new messages arrive
  // Don't auto-scroll on initial load when there are no messages
  useEffect(() => {
    if (isUserAtBottom && chatState.messages.length > 0) {
      scrollToBottom();
    }
  }, [chatState.messages, isUserAtBottom, scrollToBottom]);

  // Handle loading state changes - only scroll if user is at bottom and there are messages
  useEffect(() => {
    if (
      !chatState.isLoading &&
      isUserAtBottom &&
      chatState.messages.length > 0
    ) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [
    chatState.isLoading,
    isUserAtBottom,
    scrollToBottom,
    chatState.messages.length,
  ]);

  // Message handling functions
  const capitalizeAgentName = (key: string) => {
    if (!key) return "Agent";
    return key.charAt(0).toUpperCase() + key.slice(1);
  };

  const sendMessage = useCallback(
    async (messageContent: string) => {
      if (!messageContent.trim() || chatState.isLoading) return;

      // Add user message
      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        sender: "user",
        content: messageContent.trim(),
        timestamp: new Date(),
        senderName: "You",
      };

      setChatState((prev) => ({
        ...prev,
        messages: [...prev.messages, userMessage],
        isLoading: true,
      }));

      try {
        // Call agent API
        const response = await chatWithAgent({
          userMessage: messageContent.trim(),
          sessionId: chatState.sessionId,
        });

        // Add agent response
        const agentMessage: ChatMessage = {
          id: `${activeAgent.agentKey}-${Date.now()}`,
          sender: activeAgent.agentKey,
          content: response,
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent.agentName),
        };

        setChatState((prev) => ({
          ...prev,
          messages: [...prev.messages, agentMessage],
          isLoading: false,
        }));
      } catch (error) {
        console.error(
          `Error sending message to ${activeAgent.agentKey}:`,
          error
        );

        // Add error message
        const errorMessage: ChatMessage = {
          id: `error-${Date.now()}`,
          sender: activeAgent.agentKey,
          content: "Sorry, I encountered an error. Please try again.",
          timestamp: new Date(),
          senderName: capitalizeAgentName(activeAgent.agentName),
        };

        setChatState((prev) => ({
          ...prev,
          messages: [...prev.messages, errorMessage],
          isLoading: false,
        }));
      }
    },
    [chatWithAgent, chatState.sessionId, chatState.isLoading, activeAgent]
  );

  // Handle agent switching - clear chat state when agent changes
  useEffect(() => {
    if (currentAgentRef.current !== activeAgent.agentKey) {
      // Agent has switched, clear chat state and show switching indicator
      setIsAgentSwitching(true);
      setChatState({
        messages: [],
        isLoading: false,
        sessionId: generateSecureSessionId(),
      });

      // Reset auto-send flag for new agent
      hasAutoSentMessage.current = false;

      // Update current agent ref
      currentAgentRef.current = activeAgent.agentKey;

      // Clear switching indicator after a brief moment
      setTimeout(() => {
        setIsAgentSwitching(false);
      }, 500);
    }
  }, [activeAgent.agentKey]);

  // Auto-send message from HeroSection - use useCallback to prevent re-runs
  const autoSendMessage = useCallback(() => {
    if (
      locationState?.userMessage &&
      chatState.messages.length === 0 &&
      !hasAutoSentMessage.current &&
      !chatState.isLoading &&
      !isAgentSwitching &&
      isFromHeroSection
    ) {
      hasAutoSentMessage.current = true;
      sendMessage(locationState.userMessage);
    }
  }, [
    locationState?.userMessage,
    chatState.messages.length,
    chatState.isLoading,
    isAgentSwitching,
    isFromHeroSection,
    sendMessage,
  ]);

  // Auto-send message from HeroSection
  useEffect(() => {
    autoSendMessage();
  }, [autoSendMessage]);

  if (!agentSuite) {
    if (isLoadingSuites) {
      return <MainLoaderSkeleton />;
    } else {
      return (
        <div className="-mt-20 flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="mb-4 text-2xl font-bold text-blackOne">
              Agent Not Found
            </h1>
            <p className="mb-6 text-gray-600">
              The agent you're looking for doesn't exist.
            </p>
            <Link
              to={ROUTES.HOME}
              className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
            >
              Back to Home
            </Link>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="min-h-screen">
      <div className="mx-auto max-w-7xl p-4 lg:px-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* --- Main Content --- */}
          <div className="lg:col-span-2">
            {/* Agent Header */}
            <div className="relative h-[198px] overflow-hidden rounded-lg border bg-gray-200 bg-cover bg-center">
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center"
                style={{
                  backgroundImage: agentSuite.avatar
                    ? `url(${agentSuite.avatar})`
                    : `url(${setIq})`,
                }}
              />
              {/* Dark Overlay */}
              <div className="absolute inset-0 bg-black/20" />
              {/* Content */}
              <div className="relative z-10 flex h-full flex-col justify-center p-6">
                <h1 className="w-fit rounded bg-white px-4 py-2 text-[32px] font-bold backdrop-blur-sm">
                  {agentSuite.agentSuiteName}
                </h1>

                <h2 className="mt-8 w-fit rounded text-[20px] font-semibold text-white">
                  {agentSuite.description}
                </h2>
                <div className="font-inter text-lg text-white">
                  {agentSuite.roleDescription}
                </div>
              </div>
            </div>

            {/* Active agent */}
            {chatState.messages && chatState.messages.length === 0 && (
              <div className="relative mt-4 w-full rounded-[14px] border border-peachTwo">
                <div
                  ref={agentFeaturesRef}
                  className="flex cursor-pointer items-center gap-4 rounded-lg p-6"
                  onClick={toggleAgentFeatures}
                >
                  <div className="h-12 rounded-full bg-peachTwo">
                    <img
                      key={activeAgent.agentKey}
                      src={activeAgent.avatar}
                      className="h-full object-cover"
                      alt={activeAgent.agentName}
                      onError={(e) => {
                        const fallbackAgent = mockAgents.find(
                          (agent) =>
                            agent.id.toLowerCase() ===
                            activeAgent.agentKey.toLowerCase()
                        );
                        if (fallbackAgent) {
                          (e.target as HTMLImageElement).src =
                            fallbackAgent.image;
                        }
                      }}
                    />
                  </div>
                  <div className="flex items-center gap-3">
                    <div>
                      <p className="text-darkGray">
                        Hi, I'm{" "}
                        <span className="font-semibold">
                          {`${activeAgent.agentName} — ${activeAgent.description}`}
                        </span>{" "}
                      </p>
                      <p>{activeAgent.roleDescription}</p>
                    </div>
                    <ChevronRight
                      className={`${
                        showAgentFeatures ? "-rotate-90" : "rotate-0"
                      } text-primary`}
                    />
                  </div>
                </div>

                {/* Marketplace agent features */}
                {showAgentFeatures && (
                  <div className="mb-2 w-full border-t border-peachTwo p-6">
                    <div className="flex flex-col gap-4">
                      {activeAgent.roles.map((feature, index) => (
                        <div key={index} className="flex items-center gap-4">
                          <img
                            src={featureIcons[index % 3]}
                            alt=""
                            className="w-5"
                          />
                          <p className="text-sm text-blackTwo">{feature}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Enhanced Chat Interface */}
            <div className="bg-white font-inter">
              {chatState.messages.length === 0 ? (
                // Initial state - only show input field
                <div className="flex h-20 items-center">
                  <ChatInput
                    onSendMessage={sendMessage}
                    placeholder="I'm ready when you are..."
                    disabled={chatState.isLoading || isAgentSwitching}
                  />
                </div>
              ) : (
                // Full chat interface with messages
                <div className="relative flex h-[calc(100vh-320px)] flex-col">
                  {/* Messages Container */}
                  <div
                    ref={messagesContainerRef}
                    className="flex-1 overflow-y-auto py-4"
                    style={{ minHeight: 0 }}
                  >
                    {chatState.messages.map((message) => (
                      <MessageComponent
                        key={message.id}
                        message={message}
                        agentAvatar={activeAgent.avatar}
                      />
                    ))}

                    {/* Typing Indicator */}
                    {(chatState.isLoading || isAgentSwitching) && (
                      <TypingIndicator
                        agentImageSrc={activeAgent.avatar || vesa}
                        agentName={capitalizeAgentName(activeAgent.agentName)}
                        message={
                          isAgentSwitching
                            ? `${capitalizeAgentName(activeAgent.agentName)} is connecting`
                            : undefined
                        }
                      />
                    )}
                  </div>

                  {/* Floating Down Arrow */}
                  {showScrollToBottom && (
                    <button
                      className="absolute bottom-24 right-6 z-20 flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 bg-white shadow-lg transition hover:bg-gray-50"
                      onClick={scrollToBottom}
                      aria-label="Scroll to latest message"
                    >
                      <ArrowDown className="h-6 w-6 text-primary" />
                    </button>
                  )}

                  {/* Chat Input */}
                  <div className="flex-shrink-0 px-4 py-4">
                    <ChatInput
                      onSendMessage={sendMessage}
                      placeholder="I'm here — whenever you're ready."
                      disabled={chatState.isLoading || isAgentSwitching}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* --- Sidebar --- */}
          <div className="lg:col-span-1">
            {isLoadingAgents ? (
              <AgentSuiteSkeletonLoader count={4} />
            ) : (
              <div className="flex flex-col gap-4">
                {agents.map((agent) => (
                  <AgentCard
                    key={agent.agentKey}
                    className="w-full max-w-[334px]"
                    agent={agent}
                    showChatButton
                    isActiveAgent={activeAgent.agentKey === agent.agentKey}
                    link={ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(
                      agent.agentKey
                    )}
                    onAgentSelect={() => {
                      // Clear HeroSection influence when manually selecting agent
                      setIsFromHeroSection(false);
                      // Clear location state by replacing history
                      window.history.replaceState(
                        null,
                        "",
                        window.location.pathname
                      );
                      setActiveAgent(agent.agentKey);
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentDetailsPage;
