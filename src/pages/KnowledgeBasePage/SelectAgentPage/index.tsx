import React from 'react';
import AgentSelectionLayout from '../../../components/layout/AgentSelectionLayout';
import { Navigate, useNavigate } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';
import { knowledgeBaseBg } from '@/assets/images';
import { useGetUser } from '@/hooks/useUser';

const KnowledgeBaseSelectAgentPage: React.FC = () => {
  const navigate = useNavigate();
  const { data: userData, isLoading } = useGetUser();

  // Check if user has claimed the specific agents suite
  const isAgentSuiteClaimed = (suiteKey: string) => {
    return userData?.userInfo?.tenant?.claimedAgentSuites?.some(
      (claimedSuite) => claimedSuite.suite.agentSuiteKey === suiteKey
    );
  };

  if (!isLoading && userData?.userInfo?.tenant?.claimedAgentSuites?.length && userData.userInfo.tenant.claimedAgentSuites.length > 0) {
    return <Navigate to={ROUTES.DASHBOARD_KNOWLEDGE_BASE} replace />;
  }

  return (
    <AgentSelectionLayout
      title="Adaptive Process Library"
      description="Equip each agent with shared policies and tailored addendums, ensuring accuracy, consistency, and speed."
      bgImage={knowledgeBaseBg}
      pageType="knowledge-base"
      onAgentSuiteClick={(suite) => {
        if (!isAgentSuiteClaimed(suite.agentSuiteKey)) {
          navigate(
            ROUTES.DASHBOARD_KNOWLEDGE_BASE_ACTIVATE_SUITE(suite.agentSuiteKey)
          );
        } else {
          navigate(ROUTES.DASHBOARD_KNOWLEDGE_BASE);
        }
      }}
    />
  );
};

export default KnowledgeBaseSelectAgentPage;
