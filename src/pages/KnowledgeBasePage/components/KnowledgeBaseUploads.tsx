import React, { useState, useEffect, useRef } from "react";
import { ChevronDown, Upload } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Agent<PERSON>uite, Agent } from "../../../types/user";
import { UploadLevel } from "./LevelSelector";
import { useGetUser } from "../../../hooks/useUser";
import { useTenant } from "../../../context/TenantContext";
import { UserBasicInfoPayload } from "../../../types/user";
import FileUploadModal from "../../AiAgentsPage/AgentActivationPage/components/FileUploadModal";
import clsx from "clsx";
import { kbIcons } from "@/data/constants";
import { scyra } from "@/assets/images";
import AnalyticsDropdown from "@/components/analytics/AnalyticsDropdown";
import { useOnClickOutside } from "@/hooks/useOnClickOutside";

interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
}

interface KnowledgeBaseUploadsProps {
  selectedLevel: UploadLevel;
  onLevelSelect: (level: UploadLevel) => void;
  onDocumentSelect: (document: KnowledgeBaseDocument) => void;
  onBack: () => void;
  onDocumentUpload?: (documentKey: string, files: File[]) => void;
}

const kbTabs = { suite: "Suite Level Selector", agent: "Agent Level Selector" };

const KnowledgeBaseUploads: React.FC<KnowledgeBaseUploadsProps> = ({
  selectedLevel,
  onLevelSelect,
  onDocumentSelect,
  // onBack,
  onDocumentUpload,
}) => {
  const [activeTab, setActiveTab] = useState<UploadLevel>(selectedLevel);
  const [selectedSuite, setSelectedSuite] = useState<string>("");
  const [selectedAgent, setSelectedAgent] = useState<string>("");
  const [suiteDropdownOpen, setSuiteDropdownOpen] = useState(false);
  const [agentDropdownOpen, setAgentDropdownOpen] = useState(false);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] =
    useState<KnowledgeBaseDocument | null>(null);

  const { data: userData } = useGetUser<UserBasicInfoPayload>();
  const { setActiveAgent, claimedSuites } = useTenant();

  // Suite level documents
  const suiteDocs: KnowledgeBaseDocument[] = [
    {
      iconUrl: "",
      name: "About Your Organization",
      key: "ABOUT_YOUR_ORGANIZATION",
      description: "Overview of who you are, what you do, and your mission.",
    },
    {
      iconUrl: "",
      name: "Department Processes",
      key: "DEPARTMENT_PROCESSES",
      description:
        "Guidelines for how your department's business function operates across roles.",
    },
    {
      iconUrl: "",
      name: "Communication Compliance Guidelines",
      key: "COMMUNICATION_COMPLIANCE_GUIDELINES",
      description:
        "Policies and standards for written and verbal communications.",
    },
    {
      iconUrl: "",
      name: "Sample Playbooks",
      key: "SAMPLE_PLAYBOOKS",
      description: "Example scripts and materials used in real scenarios.",
    },
  ];

  // Get suite options from user data
  const suiteOptions =
    userData?.userInfo?.tenant?.claimedAgentSuites?.map((suite) => ({
      id: suite.suite.agentSuiteKey,
      name: suite.suite.agentSuiteName,
      icon: suite.suite.avatar,
    })) || [];

  // Get all agents from all suites
  const allAgents =
    userData?.userInfo?.tenant?.claimedAgentSuites?.flatMap((suite) =>
      suite.suite.availableAgents.map((agent) => ({
        ...agent,
        suiteKey: suite.suite.agentSuiteKey,
      }))
    ) || [];

  // Get agent documents based on selected agent
  const getAgentDocuments = (): KnowledgeBaseDocument[] => {
    if (!selectedAgent) return [];

    const agent = allAgents.find((a) => a.agentKey === selectedAgent);
    if (!agent?.fileData) return [];

    return agent.fileData.map((file) => ({
      iconUrl: file.iconUrl,
      name: file.name,
      key: file.key,
      description: file.description,
    }));
  };

  // Initialize selections
  useEffect(() => {
    if (suiteOptions.length > 0 && !selectedSuite) {
      setSelectedSuite(suiteOptions[0].id);
    }
    if (allAgents.length > 0 && !selectedAgent) {
      setSelectedAgent(allAgents[0].agentKey);
    }
  }, [suiteOptions, allAgents, selectedSuite, selectedAgent]);

  const suiteDropdownRef = useRef<HTMLDivElement>(null);
  const agentDropdownRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(suiteDropdownRef, () => setSuiteDropdownOpen(false));
  useOnClickOutside(agentDropdownRef, () => setAgentDropdownOpen(false));

  const handleTabLevelChange = (level: UploadLevel) => {
    onLevelSelect(level);
    setActiveTab(level);
  };

  // Handle agent selection change
  const handleAgentChange = (agentKey: string) => {
    setSelectedAgent(agentKey);
    setActiveAgent(agentKey);
    setAgentDropdownOpen(false);
  };

  const handleDocumentCardClick = (
    doc: KnowledgeBaseDocument,
    event: React.MouseEvent
  ) => {
    // Check if the click was on the upload button
    const target = event.target as HTMLElement;
    if (target.closest(".upload-button")) {
      return; // Don't navigate if upload button was clicked
    }

    onDocumentSelect(doc);
  };

  const handleUploadClick = (doc: KnowledgeBaseDocument) => {
    setSelectedDocument(doc);
    setUploadModalOpen(true);
  };

  const handleFilesUpload = (files: File[]) => {
    if (onDocumentUpload && selectedDocument) {
      onDocumentUpload(selectedDocument.key, files);
    }
    setUploadModalOpen(false);
  };

  const renderDocumentCard = (doc: KnowledgeBaseDocument, index: number) => (
    <div
      key={doc.key}
      onClick={(e) => handleDocumentCardClick(doc, e)}
      className="relative h-[246px] w-60 rounded-xl border border-grayTen p-6 cursor-pointer hover:shadow-md transition-shadow"
    >
      <div className="flex items-start gap-2">
        <img
          className="h-8 w-8"
          src={doc.iconUrl}
          alt={doc.name}
          onError={(e) => {
            (e.target as HTMLImageElement).src =
              kbIcons[index % kbIcons.length];
          }}
        />

        <h3 className="font-spartan text-base font-semibold text-blackOne">
          {doc.name}
        </h3>
      </div>
      <div className="mt-2.5 text-[15px] text-subText">{doc.description}</div>
      <button
        onClick={(e) => {
          e.stopPropagation();
          handleUploadClick(doc);
        }}
        className="upload-button absolute bottom-6 flex h-9 items-center gap-2 rounded-[30px] border border-blackOne px-3.5 py-1.5 text-sm font-semibold text-blackOne hover:bg-primary hover:border-primary hover:text-white transition-colors"
      >
        <Upload className="h-4" />
        Upload Document
      </button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Back Button */}
      {/* <div className="flex items-center justify-between">
        <button
          onClick={onBack}
          className="flex items-center gap-2 text-subText hover:text-blackOne transition-colors"
        >
          ← Back
        </button>
        <div />
      </div> */}

      {/* Tab Selectors with Dropdowns */}
      <div className="flex gap-20 items-center">
        <div className="flex w-fit space-x-1 border-b border-gray-200">
          {(Object.keys(kbTabs) as UploadLevel[]).map((tab) => (
            <button
              key={tab}
              onClick={() => handleTabLevelChange(tab)}
              className={clsx(
                "px-4 py-2 text-sm font-medium transition-colors",
                activeTab === tab
                  ? "border-b-2 border-primary text-primary"
                  : "text-gray-600 hover:text-blackOne"
              )}
            >
              {kbTabs[tab]}
            </button>
          ))}
        </div>

        {/* Dropdowns */}
        <div className="flex gap-4">
          {activeTab === "suite" && (
            <div className="relative" ref={suiteDropdownRef}>
              <AnalyticsDropdown
                isOpen={suiteDropdownOpen}
                onToggle={() => setSuiteDropdownOpen(!suiteDropdownOpen)}
                currentItem={suiteOptions.find((s) => s.id === selectedSuite)}
                options={suiteOptions}
                onItemSelect={(suite) => {
                  setSelectedSuite(suite.id);
                  setSuiteDropdownOpen(false);
                }}
                placeholder="Suite"
                noOptionsMessage="No other suites available"
              />
            </div>
          )}

          {activeTab === "agent" && (
            <div className="relative" ref={agentDropdownRef}>
              <AnalyticsDropdown
                isOpen={agentDropdownOpen}
                onToggle={() => setAgentDropdownOpen(!agentDropdownOpen)}
                currentItem={allAgents
                  .map((a) => ({
                    id: a.agentKey,
                    name: a.agentName,
                    icon: a.avatar,
                  }))
                  .find((a) => a.id === selectedAgent)}
                options={allAgents.map((a) => ({
                  id: a.agentKey,
                  name: a.agentName,
                  icon: a.avatar,
                }))}
                onItemSelect={(agent) => handleAgentChange(agent.id)}
                placeholder="Agent"
                noOptionsMessage="No other agents available"
              />
            </div>
          )}
        </div>
      </div>

      {/* Document Grid */}
      <div className="w-fit grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {activeTab === "suite"
          ? suiteDocs.map((doc, index) => renderDocumentCard(doc, index))
          : getAgentDocuments().map((doc, index) =>
              renderDocumentCard(doc, index)
            )}
      </div>

      {/* Upload Modal */}
      <FileUploadModal
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        title={selectedDocument?.name || "Upload Document"}
        onFilesUpload={handleFilesUpload}
      />
    </div>
  );
};

export default KnowledgeBaseUploads;
