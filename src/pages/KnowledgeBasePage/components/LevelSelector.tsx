import React from "react";
import { AgentSuite } from "../../../types/user";
import { agentSelector } from "../../../assets/images";

export type UploadLevel = "suite" | "agent";

interface LevelOption {
  id: UploadLevel;
  title: string;
  description: string;
  image: string;
  showImage: boolean;
}

interface LevelSelectorProps {
  currentSuite?: AgentSuite;
  selectedLevel?: UploadLevel;
  onLevelSelect: (level: UploadLevel) => void;
  onNext: () => void;
}

const LevelSelector: React.FC<LevelSelectorProps> = ({
  currentSuite,
  selectedLevel,
  onLevelSelect,
  onNext,
}) => {
  const levelOptions: LevelOption[] = [
    {
      id: "suite" as const,
      title: "Suite Level",
      description: "Shared by all agents in this suite",
      image: currentSuite?.avatar || "",
      showImage: true,
    },
    {
      id: "agent" as const,
      title: "Agent Level",
      description: "Only used by the selected agent",
      image: agentSelector,
      showImage: true,
    },
  ];

  const handleCardClick = (level: UploadLevel) => {
    onLevelSelect(level);
    onNext();
  };

  return (
    <div className="flex flex-col items-start">
      {/* Header */}
      <h2 className="text-lg mb-6 font-semibold capitalize text-subText">
        Knowledge base document upload selector
      </h2>

      {/* Selection Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {levelOptions.map((option) => (
          <button
            key={option.id}
            onClick={() => handleCardClick(option.id)}
            className={`flex h-[124px] w-full max-w-[285px] overflow-hidden rounded-xl border-2 text-left shadow-[0px_1px_10px_0px_#98A2B30F] transition-all hover:border-primary ${
              selectedLevel === option.id
                ? "border-primary bg-orange-50"
                : "border-[#121212] bg-white"
            }`}
          >
            {/* Image */}
            {option.showImage ? (
              <div className="flex w-[111px] items-center justify-center bg-[#F9FAFB]">
                {option.image && (
                  <img
                    src={option.image}
                    alt={option.title}
                    className="h-full w-full object-cover"
                  />
                )}
              </div>
            ) : (
              <div className="w-[111px] overflow-hidden rounded-l-lg bg-[#F9FAFB]"></div>
            )}

            {/* Content */}
            <div className="flex flex-col gap-4 p-4">
              <h3 className="text-base font-semibold text-blackOne">
                {option.title}
              </h3>
              <p className="text-sm text-blackOne">{option.description}</p>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default LevelSelector;
