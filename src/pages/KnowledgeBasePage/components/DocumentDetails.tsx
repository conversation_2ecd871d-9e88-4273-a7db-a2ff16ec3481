import React, { useState, useEffect } from "react";
import { Trash2, Edit, Upload, ChevronLeft } from "lucide-react";
import AnimatedModal from "../../../components/common/AnimatedModal";
import FileUploadModal from "../../AiAgentsPage/AgentActivationPage/components/FileUploadModal";

interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
  uploadedAt?: string;
}

interface DocumentDetailsProps {
  document: KnowledgeBaseDocument;
  onBack: () => void;
  onDelete?: (documentKey: string) => void;
  onUpload?: (documentKey: string, files: File[]) => void;
  onReplace?: (documentKey: string, files: File[]) => void;
  isLoading?: boolean;
}

const DocumentDetails: React.FC<DocumentDetailsProps> = ({
  document,
  onBack,
  onDelete,
  onUpload,
  onReplace,
  isLoading = false,
}) => {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [hasFile, setHasFile] = useState(document.hasFile || false);

  useEffect(() => {
    setHasFile(document.hasFile || false);
  }, [document.hasFile]);

  const handleDelete = () => {
    if (onDelete) {
      onDelete(document.key);
      setDeleteModalOpen(false);
      // After successful delete, update hasFile state
      setHasFile(false);
    }
  };

  const handleUpload = (files: File[]) => {
    if (hasFile && onReplace) {
      onReplace(document.key, files);
    } else if (onUpload) {
      onUpload(document.key, files);
    }
    setUploadModalOpen(false);
    // After successful upload, update hasFile state
    setHasFile(true);
  };

  const handleEdit = () => {
    console.log("Edit functionality to be implemented");
  };

  return (
    <div className="space-y-6">
      {/* Header with Back Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            onClick={onBack}
            className="flex items-center justify-center w-5 h-5 rounded-lg transition-colors"
          >
            <ChevronLeft className="h-5 w-5 text-subText hover:text-primary" />
          </button>
          <h1 className="font-semibold text-blackOne font-spartan mt-0.5">
            {document.name}
          </h1>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={() => setDeleteModalOpen(true)}
            disabled={!hasFile || isLoading}
            className={`
            flex items-center gap-2 px-4 py-2 rounded-[30px] border border-blackOne transition-colors
            ${
              hasFile && !isLoading
                ? "hover:bg-red-50 border-blackOne hover:border-red-500"
                : "border-grayThirteen text-grayThirteen cursor-not-allowed"
            }
          `}
          >
            <Trash2 className="h-4 w-4" />
          </button>

          <button
            onClick={handleEdit}
            disabled={!hasFile || isLoading}
            className={`
            flex items-center gap-2 px-4 py-2 rounded-[30px] border transition-colors
            ${
              hasFile && !isLoading
                ? "hover:bg-grayTwo border-blackOne hover:border-primary"
                : "border-grayThirteen text-grayThirteen cursor-not-allowed"
            }
          `}
          >
            <Edit className="h-4 w-4" />
          </button>

          <button
            onClick={() => setUploadModalOpen(true)}
            disabled={isLoading}
            className={`
            flex items-center gap-2 px-4 py-2 rounded-[30px] border border-blackOne transition-colors text-sm font-semibold
            ${
              isLoading
                ? "bg-grayTwo text-grayThirteen cursor-not-allowed"
                : "hover:bg-primary/90 hover:text-white hover:border-primary/90"
            }
          `}
          >
            <Upload className="h-4 w-4" />
            {hasFile ? "Replace Document" : "Upload Document"}
          </button>
        </div>
      </div>

      {/* File Status */}
      {hasFile && document.fileName && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm font-medium text-green-800">
              File uploaded: {document.fileName}
            </span>
          </div>
          {document.uploadedAt && (
            <p className="text-xs text-green-600 mt-1">
              Uploaded on {new Date(document.uploadedAt).toLocaleDateString()}
            </p>
          )}
        </div>
      )}

      {/* Overview Section */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="text-lg font-bold">Overview</div>
        <p className="font-medium mt-2.5">{document.description}</p>
      </div>

      {/* Delete Confirmation Modal */}
      <AnimatedModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Delete Document"
        maxWidth="md"
      >
        <div className="space-y-4">
          <p className="text-subText">
            You're going to delete the '{document.name}' document.
          </p>
          <p className="text-sm text-red-600">
            This action cannot be undone. The document will be permanently
            removed.
          </p>
          <div className="flex gap-3 justify-end pt-4">
            <button
              onClick={() => setDeleteModalOpen(false)}
              className="px-4 py-2 border border-grayTen rounded-lg text-subText hover:bg-grayTwo transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Delete
            </button>
          </div>
        </div>
      </AnimatedModal>

      {/* Upload Modal */}
      <FileUploadModal
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        title={hasFile ? `Replace ${document.name}` : `Upload ${document.name}`}
        onFilesUpload={handleUpload}
      />
    </div>
  );
};

export default DocumentDetails;
