import React, { useEffect, useRef, useState } from "react";
import { BookOpen } from "lucide-react";
import AppContainer from "@/components/common/AppContainer";
import { motion } from "framer-motion";
import EnhancedChatSidebar from "../../components/common/EnhancedChatSidebar";
import { useTenant } from "@/context/TenantContext";
import { AgentSuite } from "../../types/user";
import LevelSelector, { UploadLevel } from "./components/LevelSelector";
import KnowledgeBaseUploads from "./components/KnowledgeBaseUploads";
import DocumentDetails from "./components/DocumentDetails";
import {
  useSuiteKnowledgeBaseApi,
  useAgentKnowledgeBaseApi,
  KnowledgeBaseUploadRequest,
  KnowledgeBaseDeleteRequest,
} from "../../services/knowledgeBaseService";
import { useCustomToast } from "../../hooks/useToast";
import { useLocation } from "react-router-dom";

export type KnowledgeBaseStep =
  | "level-selector"
  | "uploads"
  | "document-details";

interface KnowledgeBaseDocument {
  iconUrl: string;
  name: string;
  key: string;
  description: string;
  hasFile?: boolean;
  fileName?: string;
  uploadedAt?: string;
}

interface KnowledgeBaseState {
  step: KnowledgeBaseStep;
  selectedLevel?: UploadLevel;
  selectedDocument?: KnowledgeBaseDocument;
}

const KnowledgeBasePage: React.FC = () => {
  const { activeAgent, claimedSuites } = useTenant();
  const location = useLocation();
  const locationStateSuite = location.state as AgentSuite;
  // Get current suite
  const currentSuite: AgentSuite | undefined =
    locationStateSuite || claimedSuites[0]?.suite;

  const { successToast, errorToast } = useCustomToast();

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // API hooks
  const suiteKnowledgeBaseApi = useSuiteKnowledgeBaseApi();
  const agentKnowledgeBaseApi = useAgentKnowledgeBaseApi();

  // Component state
  const [state, setState] = useState<KnowledgeBaseState>({
    step: "level-selector",
  });
  const [isLoading, setIsLoading] = useState(false);

  // Trigger reload of chat history when agent changes
  useEffect(() => {
    if (!activeAgent) return;

    const reloadForAgent = async () => {
      try {
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }
      } catch (error) {
        console.error("Error occured while changing agent:", error);
      }
    };

    reloadForAgent();
  }, [activeAgent]);

  // State update helper
  const updateState = (updates: Partial<KnowledgeBaseState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  // Navigation handlers
  const handleLevelSelect = (level: UploadLevel) => {
    updateState({ selectedLevel: level });
  };

  const handleNext = () => {
    if (state.step === "level-selector") {
      updateState({ step: "uploads" });
    }
  };

  const handleBack = () => {
    if (state.step === "uploads") {
      updateState({ step: "level-selector" });
    } else if (state.step === "document-details") {
      updateState({ step: "uploads" });
    }
  };

  const handleDocumentSelect = (document: KnowledgeBaseDocument) => {
    updateState({
      step: "document-details",
      selectedDocument: document,
    });
  };

  // Document action handlers with API integration
  const handleDocumentDelete = async (documentKey: string) => {
    if (!state.selectedLevel) return;

    setIsLoading(true);
    try {
      const deleteRequest: KnowledgeBaseDeleteRequest = {
        fileTag: documentKey,
      };

      if (state.selectedLevel === "suite" && currentSuite) {
        await suiteKnowledgeBaseApi.deleteSuiteKnowledgeBase(
          currentSuite.agentSuiteKey,
          deleteRequest
        );
      } else if (state.selectedLevel === "agent") {
        await agentKnowledgeBaseApi.deleteAgentKnowledgeBase(deleteRequest);
      }

      successToast("Document deleted successfully");

      // Update document state to reflect deletion
      if (state.selectedDocument) {
        setState((prev) => ({
          ...prev,
          selectedDocument: {
            ...prev.selectedDocument!,
            hasFile: false,
            fileName: undefined,
            uploadedAt: undefined,
          },
        }));
      }
    } catch (error) {
      console.error("Delete document error:", error);
      errorToast(
        error instanceof Error ? error.message : "Failed to delete document"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentUpload = async (documentKey: string, files: File[]) => {
    if (!files.length || !state.selectedLevel) return;

    setIsLoading(true);
    try {
      const uploadRequest: KnowledgeBaseUploadRequest = {
        filePart: files[0],
        fileTag: documentKey,
      };
      console.log("We got here");

      if (state.selectedLevel === "suite" && currentSuite) {
        console.log("Did we get here tho?");
        await suiteKnowledgeBaseApi.uploadSuiteKnowledgeBase(
          currentSuite.agentSuiteKey,
          uploadRequest
        );
      } else if (state.selectedLevel === "agent") {
        await agentKnowledgeBaseApi.uploadAgentKnowledgeBase(uploadRequest);
      }

      successToast("Document uploaded successfully");

      // Update document state to reflect upload
      if (state.selectedDocument) {
        setState((prev) => ({
          ...prev,
          selectedDocument: {
            ...prev.selectedDocument!,
            hasFile: true,
            fileName: files[0].name,
            uploadedAt: new Date().toISOString(),
          },
        }));
      }
    } catch (error) {
      console.error("Upload document error:", error);
      errorToast(
        error instanceof Error ? error.message : "Failed to upload document"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentReplace = async (documentKey: string, files: File[]) => {
    if (!files.length || !state.selectedLevel) return;

    setIsLoading(true);
    try {
      const uploadRequest: KnowledgeBaseUploadRequest = {
        filePart: files[0],
        fileTag: documentKey,
      };

      if (state.selectedLevel === "suite" && currentSuite) {
        await suiteKnowledgeBaseApi.replaceSuiteKnowledgeBase(
          currentSuite.agentSuiteKey,
          uploadRequest
        );
      } else if (state.selectedLevel === "agent") {
        await agentKnowledgeBaseApi.replaceAgentKnowledgeBase(uploadRequest);
      }

      successToast("Document replaced successfully");

      // Update document state to reflect replacement
      if (state.selectedDocument) {
        setState((prev) => ({
          ...prev,
          selectedDocument: {
            ...prev.selectedDocument!,
            hasFile: true,
            fileName: files[0].name,
            uploadedAt: new Date().toISOString(),
          },
        }));
      }
    } catch (error) {
      console.error("Replace document error:", error);
      errorToast(
        error instanceof Error ? error.message : "Failed to replace document"
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Render current step
  const renderStep = () => {
    switch (state.step) {
      case "level-selector":
        return (
          <LevelSelector
            currentSuite={currentSuite}
            selectedLevel={state.selectedLevel}
            onLevelSelect={handleLevelSelect}
            onNext={handleNext}
          />
        );
      case "uploads":
        return (
          <KnowledgeBaseUploads
            selectedLevel={state.selectedLevel!}
            onDocumentSelect={handleDocumentSelect}
            onBack={handleBack}
            onDocumentUpload={handleDocumentUpload}
          />
        );
      case "document-details":
        return state.selectedDocument ? (
          <DocumentDetails
            document={state.selectedDocument}
            onBack={handleBack}
            onDelete={handleDocumentDelete}
            onUpload={handleDocumentUpload}
            onReplace={handleDocumentReplace}
            isLoading={isLoading}
          />
        ) : null;
      default:
        return null;
    }
  };

  // TODO: Optimize and add back after updating the component to use the new design @joshua 2025-08-29
  // if (!hasClaimedTenants) {
  //   return (
  //     <Navigate to={ROUTES.DASHBOARD_KNOWLEDGE_BASE_SELECT_AGENT} replace />
  //   );
  // }

  // Main Knowledge Base page for users with claimed tenants
  return (
    <div className="flex flex-col h-full">
      <div className="flex overflow-hidden flex-1">
        {/* Main Content - Scrollable */}
        <div className="flex overflow-hidden flex-1">
          {/* Chat Sidebar */}
          <EnhancedChatSidebar reloadChatHistoryRef={reloadChatHistoryRef} />

          <div className="overflow-y-auto flex-1">
            <AppContainer className="p-8 space-y-6 lg:space-y-8">
              {/* Header - Only show for uploads and document-details steps */}
              {(state.step === "level-selector" ||
                state.step === "uploads" ||
                state.step === "document-details") && (
                <div className="flex justify-between items-center mb-8">
                  <div className="flex items-center">
                    <BookOpen className="mr-3 w-5 h-5 text-primary md:h-6 md:w-6" />
                    <h1 className="text-2xl font-semibold text-[#403F3E]">
                      Knowledge Base
                    </h1>
                  </div>
                </div>
              )}

              {/* Step Content */}
              <motion.div
                key={state.step}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderStep()}
              </motion.div>
            </AppContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBasePage;
