import React from 'react';
import { Calendar } from 'lucide-react';
import { TaskLogDetailsFormData } from '@/types/taskLog';

interface TaskLogBasicInfoProps {
  taskLog: TaskLogDetailsFormData;
  editForm: Partial<TaskLogDetailsFormData>;
  isEditing: boolean;
  onInputChange: (field: keyof TaskLogDetailsFormData, value: string) => void;
}

const TaskLogBasicInfo: React.FC<TaskLogBasicInfoProps> = ({
  taskLog,
  editForm,
  isEditing,
  onInputChange
}) => {
  return (
    <div className="space-y-8">
      {/* Top Section - Time, Date Created, Due Date */}
      <div className="grid grid-cols-3 gap-6 mb-8">
        <div className="p-4 bg-gray-100 rounded-lg">
          <h3 className="mb-2 text-sm font-medium text-gray-600">Time (EST)</h3>
          <p className="text-lg text-gray-900">{taskLog.time}</p>
        </div>
        
        <div className="p-4 bg-gray-100 rounded-lg">
          <h3 className="mb-2 text-sm font-medium text-gray-600">Date Created</h3>
          <p className="text-lg text-gray-900">{taskLog.dateCreated}</p>
        </div>
        
        <div className={`rounded-lg p-4 ${isEditing ? 'bg-white border-2 border-orange-500' : 'bg-gray-100'}`}>
          <h3 className="mb-2 text-sm font-medium text-gray-600">Due Date</h3>
          {isEditing ? (
            <div className="flex gap-2 items-center">
              <input
                type="text"
                value={editForm.dueDate || ''}
                onChange={(e) => onInputChange('dueDate', e.target.value)}
                className="flex-1 text-lg text-gray-900 bg-transparent outline-none"
                placeholder="Enter due date"
              />
              <Calendar className="w-4 h-4 text-orange-500" />
            </div>
          ) : (
            <p className="text-lg text-gray-900">{taskLog.dueDate}</p>
          )}
        </div>
      </div>

      {/* Task Title */}
      <div className="mb-8">
        <h3 className="mb-3 text-sm font-medium text-gray-600">Task Title</h3>
        {isEditing ? (
          <input
            type="text"
            value={editForm.taskTitle || ''}
            onChange={(e) => onInputChange('taskTitle', e.target.value)}
            className="p-3 w-full text-lg text-gray-900 bg-white rounded-lg border-2 border-orange-500 outline-none"
            placeholder="Enter task title"
          />
        ) : (
          <p className="text-lg text-gray-900">{taskLog.taskTitle}</p>
        )}
      </div>

      {/* Description */}
      <div className="mb-8">
        <h3 className="mb-3 text-sm font-medium text-gray-600">Description</h3>
        {isEditing ? (
          <textarea
            value={editForm.description || ''}
            onChange={(e) => onInputChange('description', e.target.value)}
            rows={8}
            className="p-3 w-full text-gray-900 bg-white rounded-lg border-2 border-orange-500 outline-none resize-none"
            placeholder="Enter task description"
          />
        ) : (
          <div className="p-4 bg-gray-100 rounded-lg">
            <p className="leading-relaxed text-gray-900">{taskLog.description}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskLogBasicInfo;