import React from 'react';
import { ChevronLeft, Edit, Trash2, Save, X } from 'lucide-react';

interface TaskLogHeaderProps {
  taskTitle: string;
  isEditing: boolean;
  saving: boolean;
  onBack: () => void;
  onEdit: () => void;
  onSave: () => void;
  onCancel: () => void;
  onDelete?: () => void;
}

const TaskLogHeader: React.FC<TaskLogHeaderProps> = ({
  taskTitle,
  isEditing,
  saving,
  onBack,
  onEdit,
  onSave,
  onCancel,
  onDelete
}) => {
  return (
    <div className="px-6 py-4 bg-white border-b border-gray-200">
      <div className="flex justify-between items-center">
        <div className="flex gap-4 items-center">
          <button 
            onClick={onBack}
            className="flex items-center text-gray-600 transition-colors hover:text-gray-900"
          >
            <ChevronLeft className="mr-1 w-5 h-5" />
            {taskTitle}
          </button>
        </div>
        
        <div className="flex gap-3 items-center">
          {onDelete && (
            <button 
              className="p-2 text-gray-600 rounded-lg transition-colors hover:text-gray-900 hover:bg-gray-100"
              onClick={onDelete}
            >
              <Trash2 className="w-5 h-5" />
            </button>
          )}
          
          {isEditing ? (
            <button 
              onClick={onSave}
              disabled={saving}
              className="flex gap-2 items-center px-4 py-2 text-white bg-orange-500 rounded-lg transition-colors hover:bg-orange-600 disabled:opacity-50"
            >
              {saving ? (
                <div className="w-4 h-4 rounded-full border-2 border-white animate-spin border-t-transparent" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              Save Changes
            </button>
          ) : (
            <button 
              onClick={onEdit}
              className="p-2 text-gray-600 rounded-lg transition-colors hover:text-gray-900 hover:bg-gray-100"
            >
              <Edit className="w-5 h-5" />
            </button>
          )}
          
          {isEditing && (
            <button 
              onClick={onCancel}
              className="p-2 text-gray-600 rounded-lg transition-colors hover:text-gray-900 hover:bg-gray-100"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskLogHeader;