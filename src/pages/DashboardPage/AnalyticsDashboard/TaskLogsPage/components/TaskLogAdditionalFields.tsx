import React from 'react';
import { User, Type, AlertTriangle } from 'lucide-react';
import { TaskLogDetailsFormData } from '@/types/taskLog';

interface TaskLogAdditionalFieldsProps {
  taskLog: TaskLogDetailsFormData;
  editForm: Partial<TaskLogDetailsFormData>;
  isEditing: boolean;
  onInputChange: (field: keyof TaskLogDetailsFormData, value: string) => void;
}

const TaskLogAdditionalFields: React.FC<TaskLogAdditionalFieldsProps> = ({
  taskLog,
  editForm,
  isEditing,
  onInputChange
}) => {
  const fieldConfig = [
    {
      key: 'assignedBy' as keyof TaskLogDetailsFormData,
      label: 'Assigned By',
      icon: User,
      value: taskLog.assignedBy,
      editValue: editForm.assignedBy,
      placeholder: 'Enter assignee name'
    },
    {
      key: 'type' as keyof TaskLogDetailsFormData,
      label: 'Type',
      icon: Type,
      value: taskLog.type,
      editValue: editForm.type,
      placeholder: 'Enter task type'
    },
    {
      key: 'escalationPolicy' as keyof TaskLogDetailsFormData,
      label: 'Escalation Policy',
      icon: AlertTriangle,
      value: taskLog.escalationPolicy,
      editValue: editForm.escalationPolicy,
      placeholder: 'Enter escalation policy'
    }
  ];

  return (
    <div className="space-y-6">
      {fieldConfig.map(({ key, label, icon: Icon, value, editValue, placeholder }) => (
        <div 
          key={key}
          className={`rounded-lg p-4 border ${
            isEditing ? 'bg-white border-orange-500' : 'bg-gray-50 border-gray-200'
          }`}
        >
          <div className="flex gap-3 items-center">
            <Icon className="w-5 h-5 text-orange-500" />
            <span className="text-sm font-medium text-gray-600">{label}</span>
            {isEditing ? (
              <input
                type="text"
                value={editValue || ''}
                onChange={(e) => onInputChange(key, e.target.value)}
                className="flex-1 text-gray-900 bg-transparent outline-none"
                placeholder={placeholder}
              />
            ) : (
              <span className="text-gray-900">{value}</span>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default TaskLogAdditionalFields;