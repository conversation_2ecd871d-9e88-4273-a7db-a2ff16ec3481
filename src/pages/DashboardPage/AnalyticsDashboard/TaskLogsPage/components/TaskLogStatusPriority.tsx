import React from 'react';
import { ChevronLeft } from 'lucide-react';
import { TaskLogDetailsFormData, TaskLogStatusDisplay, TaskLogPriorityDisplay } from '@/types/taskLog';

interface TaskLogStatusPriorityProps {
  taskLog: TaskLogDetailsFormData;
  editForm: Partial<TaskLogDetailsFormData>;
  isEditing: boolean;
  onSelectChange: (field: keyof TaskLogDetailsFormData, value: string) => void;
  getPriorityColor: (priority: string) => string;
  getStatusColor: (status: string) => string;
}

const TaskLogStatusPriority: React.FC<TaskLogStatusPriorityProps> = ({
  taskLog,
  editForm,
  isEditing,
  onSelectChange,
  getPriorityColor,
  getStatusColor
}) => {
  const statusOptions: TaskLogStatusDisplay[] = ['Not Started', 'In Progress', 'Completed'];
  const priorityOptions: TaskLogPriorityDisplay[] = ['Low', 'Medium', 'High', 'Critical'];

  const getPriorityIcon = (priority: string) => {
    const iconMap = {
      'Low': '🟢',
      'Medium': '🟠', 
      'High': '🔴',
      'Critical': '⚫'
    };
    return iconMap[priority as keyof typeof iconMap] || '🟠';
  };

  return (
    <div className="grid grid-cols-2 gap-6 mb-8">
      <div>
        <h3 className="mb-3 text-sm font-medium text-gray-600">Status</h3>
        {isEditing ? (
          <div className="relative">
            <select
              value={editForm.status || ''}
              onChange={(e) => onSelectChange('status', e.target.value)}
              className="px-4 py-3 w-full text-white bg-gray-600 rounded-lg appearance-none cursor-pointer outline-none"
            >
              {statusOptions.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            <div className="flex absolute inset-y-0 right-0 items-center pr-3 pointer-events-none">
              <ChevronLeft className="w-4 h-4 text-white rotate-[-90deg]" />
            </div>
          </div>
        ) : (
          <span className={`inline-flex px-4 py-2 rounded-lg text-sm font-medium ${getStatusColor(taskLog.status)}`}>
            {taskLog.status}
          </span>
        )}
      </div>
      
      <div>
        <h3 className="mb-3 text-sm font-medium text-gray-600">Priority</h3>
        {isEditing ? (
          <div className="relative">
            <select
              value={editForm.priority || ''}
              onChange={(e) => onSelectChange('priority', e.target.value)}
              className="px-4 py-3 w-full text-white bg-orange-500 rounded-lg appearance-none cursor-pointer outline-none"
            >
              {priorityOptions.map(priority => (
                <option key={priority} value={priority}>
                  {getPriorityIcon(priority)} {priority}
                </option>
              ))}
            </select>
            <div className="flex absolute inset-y-0 right-0 items-center pr-3 pointer-events-none">
              <ChevronLeft className="w-4 h-4 text-white rotate-[-90deg]" />
            </div>
          </div>
        ) : (
          <span className={`inline-flex px-4 py-2 rounded-lg text-sm font-medium ${getPriorityColor(taskLog.priority)}`}>
            {getPriorityIcon(taskLog.priority)} {taskLog.priority}
          </span>
        )}
      </div>
    </div>
  );
};

export default TaskLogStatusPriority;