import React from 'react';
import { useNavigate } from 'react-router-dom';
import DataTable, { Column } from '@/components/analytics/DataTable';
import { useAnalyticsParams } from '@/utils/urlParams';
import { ROUTES } from '@/constants/routes';
import { useTaskLogsList } from '@/hooks/useTaskLog';
import { useTenant } from '@/context/TenantContext';
import { TaskLog, mapStatusToDisplay, mapPriorityToDisplay } from '@/types/taskLog';

interface TaskLogTableRow {
  id: string;
  dateCreated: string;
  time: string;
  taskTitle: string;
  assignedBy: string;
  type: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical' | 'Urgent';
  status: 'Completed' | 'In Progress' | 'Not Started' | 'Cancelled';
  escalationPolicy: string;
}

const TaskLogsPage: React.FC = () => {
  const { filters } = useAnalyticsParams();
  const navigate = useNavigate();
  const { tenantId } = useTenant();

  // Use the hook to fetch task logs
  const { data: taskLogs = [], isLoading } = useTaskLogsList(tenantId || '', !!tenantId);

  // Convert TaskLog data to table format
  const convertToTableData = (taskLogs: TaskLog[]): TaskLogTableRow[] => {
    return taskLogs.map(log => ({
      id: log.id,
      dateCreated: new Date(log.createdAt).toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      }),
      time: new Date(log.createdAt).toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      taskTitle: log.taskTitle,
      assignedBy: log.assignedTo || 'Unassigned',
      type: log.taskType || 'General',
      priority: mapPriorityToDisplay(log.priority),
      status: mapStatusToDisplay(log.status),
      escalationPolicy: log.escalationPolicy || 'Default',
    }));
  };

  const tableData = convertToTableData(taskLogs);

  const columns: Column<TaskLogTableRow>[] = [
    {
      key: 'dateCreated',
      label: 'Date Created',
      sortable: true,
    },
    {
      key: 'time',
      label: 'Time (EST)',
      sortable: true,
    },
    {
      key: 'taskTitle',
      label: 'Task Title',
      sortable: true,
      render: (value) => (
        <div className="max-w-xs truncate" title={String(value)}>
          {String(value)}
        </div>
      ),
    },
    {
      key: 'assignedBy',
      label: 'Assigned By',
      sortable: true,
    },
    {
      key: 'type',
      label: 'Type',
      sortable: true,
    },
    {
      key: 'priority',
      label: 'Priority',
      sortable: true,
      render: (value) => {
        const priority = value as TaskLogTableRow['priority'];
        const colorMap = {
          Low: 'bg-green-100 text-green-800',
          Medium: 'bg-yellow-100 text-yellow-800',
          High: 'bg-orange-100 text-orange-800',
          Critical: 'bg-red-100 text-red-800',
          Urgent: 'bg-purple-100 text-purple-800',
        };
        return (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorMap[priority] || 'bg-gray-100 text-gray-800'}`}>
            {priority}
          </span>
        );
      },
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => {
        const status = value as TaskLogTableRow['status'];
        const colorMap = {
          Completed: 'bg-green-100 text-green-800',
          'In Progress': 'bg-blue-100 text-blue-800',
          'Not Started': 'bg-gray-100 text-gray-800',
          'Cancelled': 'bg-red-100 text-red-800',
        };
        return (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${colorMap[status] || 'bg-gray-100 text-gray-800'}`}>
            {status}
          </span>
        );
      },
    },
    {
      key: 'escalationPolicy',
      label: 'Escalation Policy',
      render: (value) => (
        <div className="max-w-xs truncate" title={String(value)}>
          {String(value)}
        </div>
      ),
    },
  ];

  const handleRowClick = (row: TaskLogTableRow) => {
    // Navigate to task log details page
    navigate(ROUTES.DASHBOARD_ANALYTICS_TASK_LOG_DETAILS(row.id));
  };

  return (
    <div className="space-y-6">
      <div className="hidden justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-blackOne">
            Agent Task Logs
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            Track and monitor all agent task activities
            {filters.suite && ` for ${filters.suite}`}
            {filters.agent && ` - ${filters.agent}`}
          </p>
        </div>
      </div>

      <DataTable
        data={tableData}
        columns={columns}
        onRowClick={handleRowClick}
        loading={isLoading}
        emptyMessage="No task logs found"
        rowColoring={true}
        rowColoringType="odd"
      />
    </div>
  );
};

export default TaskLogsPage;