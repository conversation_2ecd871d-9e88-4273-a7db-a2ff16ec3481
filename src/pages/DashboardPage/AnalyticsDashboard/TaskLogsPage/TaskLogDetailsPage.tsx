import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTaskLogDetails, useUpdateTaskLogMutation } from '@/hooks/useTaskLog';
import { useTenant } from '@/context/TenantContext';
import { 
  TaskLogDetailsFormData, 
  mapStatusToDisplay, 
  mapPriorityToDisplay,
  mapDisplayToStatus,
  mapDisplayToPriority
} from '@/types/taskLog';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { 
  TaskLogHeader,
  TaskLogBasicInfo,
  TaskLogStatusPriority,
  TaskLogAdditionalFields,
  TaskLogDetailsLoading,
  TaskLogDetailsError
} from './components';

interface TaskLogDetailsPageProps {
  taskId?: string;
}

const TaskLogDetailsPage: React.FC<TaskLogDetailsPageProps> = ({ taskId }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { tenantId } = useTenant();
  const taskLogId = taskId || id;
  
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<TaskLogDetailsFormData>>({});

  // Use the new hook to fetch task log details
  const { 
    data: taskLog, 
    isLoading, 
    error, 
    refetch 
  } = useTaskLogDetails(taskLogId || '', !!taskLogId);

  const updateTaskLogMutation = useUpdateTaskLogMutation();

  // Convert TaskLog to TaskLogDetailsFormData for UI
  const convertToFormData = (data: any): TaskLogDetailsFormData => {
    if (!data) return {} as TaskLogDetailsFormData;
    
    return {
      taskTitle: data.taskTitle || '',
      description: data.successCriteria || 'No description provided',
      time: new Date(data.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      dateCreated: new Date(data.createdAt).toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      }),
      dueDate: data.dueDate ? new Date(data.dueDate).toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      }) : 'No due date',
      assignedBy: data.assignedTo || 'Unassigned',
      type: data.taskType || 'General Task',
      priority: mapPriorityToDisplay(data.priority),
      status: mapStatusToDisplay(data.status),
      escalationPolicy: data.escalationPolicy || 'Default Policy',
    };
  };

  const formData = convertToFormData(taskLog);

  const handleBack = () => {
    navigate(-1);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditForm(formData);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditForm(formData);
  };

  const handleSave = async () => {
    if (!taskLog || !taskLogId) return;

    try {
      const updateData = {
        id: taskLogId,
        tenantId: tenantId || taskLog.tenantId,
        status: editForm.status ? mapDisplayToStatus(editForm.status) : undefined,
        priority: editForm.priority ? mapDisplayToPriority(editForm.priority) : undefined,
        dueDate: editForm.dueDate ? new Date(editForm.dueDate).toISOString() : undefined,
        escalationPolicy: editForm.escalationPolicy,
        successCriteria: editForm.description,
        auditEvent: 'TASK_UPDATED',
        auditNotes: 'Task updated via web interface'
      };

      await updateTaskLogMutation.mutateAsync(updateData);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to save task log changes:', error);
    }
  };

  const handleInputChange = (field: keyof TaskLogDetailsFormData, value: string) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSelectChange = (field: keyof TaskLogDetailsFormData, value: string) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getPriorityColor = (priority: string) => {
    const colorMap = {
      Low: 'bg-green-500 text-white',
      Medium: 'bg-orange-500 text-white',
      High: 'bg-red-500 text-white',
      Critical: 'bg-red-600 text-white',
      Urgent: 'bg-purple-600 text-white',
    };
    return colorMap[priority as keyof typeof colorMap] || 'bg-gray-500 text-white';
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      'Completed': 'bg-green-500 text-white',
      'In Progress': 'bg-blue-500 text-white',
      'Not Started': 'bg-gray-600 text-white',
      'Cancelled': 'bg-red-500 text-white',
    };
    return colorMap[status as keyof typeof colorMap] || 'bg-gray-500 text-white';
  };

  if (isLoading) {
    return <TaskLogDetailsLoading />;
  }

  if (error) {
    return <TaskLogDetailsError message={error instanceof Error ? error.message : 'An error occurred'} onRetry={() => refetch()} />;
  }

  if (!taskLog) {
    return <TaskLogDetailsError message="Task log not found" onBack={handleBack} />;
  }

  return (
    <div className="min-h-screen">
      {/* Content */}
      <div className="flex">
        {/* Left Chat Sidebar */}
        <div className="w-96 min-h-screen">
          <EnhancedChatSidebar />
        </div>

        {/* Main Content */}
        <div className="flex flex-col flex-1 p-8">
          {/* Header */}
          <TaskLogHeader
            taskTitle={formData.taskTitle}
            isEditing={isEditing}
            saving={updateTaskLogMutation.isPending}
            onBack={handleBack}
            onEdit={handleEdit}
            onSave={handleSave}
            onCancel={handleCancelEdit}
          />
          <div className="max-w-full">
            {/* Basic Information */}
            <TaskLogBasicInfo
              taskLog={formData}
              editForm={editForm}
              isEditing={isEditing}
              onInputChange={handleInputChange}
            />

            {/* Status and Priority */}
            <TaskLogStatusPriority
              taskLog={formData}
              editForm={editForm}
              isEditing={isEditing}
              onSelectChange={handleSelectChange}
              getPriorityColor={getPriorityColor}
              getStatusColor={getStatusColor}
            />

            {/* Additional Fields */}
            <TaskLogAdditionalFields
              taskLog={formData}
              editForm={editForm}
              isEditing={isEditing}
              onInputChange={handleInputChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskLogDetailsPage;