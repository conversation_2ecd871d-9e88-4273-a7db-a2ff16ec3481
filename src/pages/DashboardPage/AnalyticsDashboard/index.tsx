import React from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Search, Filter } from 'lucide-react';
import AnimatedTabs, { Tab } from '@/components/ui/AnimatedTabs';
import SuiteAgentDropdown from '@/components/analytics/SuiteAgentDropdown';
import { Button } from '@/components/ui/Button';
import { ROUTES } from '@/constants/routes';
import { useAnalyticsParams } from '@/utils/urlParams';

const AnalyticsDashboard: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { navigateWithFilters } = useAnalyticsParams();

  const tabs: Tab[] = [
    {
      id: 'insights',
      label: 'Agent Insights',
    },
    {
      id: 'task-logs',
      label: 'Agent Task Logs',
    },
    {
      id: 'assignment-logs',
      label: 'Agent Assignment Logs',
    },
  ];

  // Determine active tab based on current path
  const getActiveTabFromPath = () => {
    const path = location.pathname;
    if (path.includes('/task-logs')) return 'task-logs';
    if (path.includes('/assignment-logs')) return 'assignment-logs';
    return 'insights'; // Default to insights
  };

  const activeTab = getActiveTabFromPath();

  // Check if we're on a child route (detail page)
  const isChildRoute = 
    location.pathname.includes('/all') ||
    location.pathname !== ROUTES.DASHBOARD_ANALYTICS_INSIGHTS &&
    location.pathname !== ROUTES.DASHBOARD_ANALYTICS_TASK_LOGS &&
    location.pathname !== ROUTES.DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS;

  const handleTabChange = (tabId: string) => {
    switch (tabId) {
      case 'insights':
        navigateWithFilters(ROUTES.DASHBOARD_ANALYTICS_INSIGHTS);
        break;
      case 'task-logs':
        navigateWithFilters(ROUTES.DASHBOARD_ANALYTICS_TASK_LOGS);
        break;
      case 'assignment-logs':
        navigateWithFilters(ROUTES.DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS);
        break;
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      <div className="overflow-y-auto flex-1 p-8 space-y-8">
        {/* Conditionally render header and tabs only on parent pages */}
        {!isChildRoute && (
          <>
            <div className="flex justify-between items-center w-full">
              {/* Tabs */}
              <AnimatedTabs
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={handleTabChange}
                showContent={false}
              />
              {/* Search and Filter - moved below tabs */}
              <div className="flex gap-3 justify-end items-center">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
                  <input
                    type="text"
                    placeholder="Search"
                    className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
                  />
                </div>
                <Button className="flex h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:border-0 hover:bg-primary hover:text-white">
                  <span>Filter</span>
                  <Filter className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Header with Dropdowns */}
            <div className="p-4 bg-white rounded-lg">
              <SuiteAgentDropdown />
            </div>
          </>
        )}

        {/* Content */}
        <div className="flex-1">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;