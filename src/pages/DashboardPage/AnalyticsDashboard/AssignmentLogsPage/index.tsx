import React, { useState } from 'react';
import DataTable, { Column } from '@/components/analytics/DataTable';
import { useAnalyticsParams } from '@/utils/urlParams';

interface AssignmentLog {
  id: string;
  assignedDate: string;
  assignedBy: string;
  assignmentTitle: string;
  startDate: string;
  dueDate: string;
  status: 'Pending' | 'In Progress';
  overdue: string;
  completionDate: string;
}

const AssignmentLogsPage: React.FC = () => {
  const { filters } = useAnalyticsParams();
  const [loading, setLoading] = useState(false);

  // Mock data based on the third screenshot
  const mockAssignmentLogs: AssignmentLog[] = [
    {
      id: '1',
      assignedDate: 'Aug 21, 2025',
      assignedBy: '<PERSON><PERSON>',
      assignmentTitle: 'Send email requesting late...',
      startDate: 'Aug 21, 2025',
      dueDate: 'Aug 21, 2025',
      status: 'Pending',
      overdue: '3 Days',
      completionDate: 'Aug 21, 2025',
    },
    {
      id: '2',
      assignedDate: 'Sep 15, 2025',
      assignedBy: '<PERSON>',
      assignmentTitle: 'Request an update on the...',
      startDate: 'Sep 15, 2025',
      dueDate: 'Sep 15, 2025',
      status: 'In Progress',
      overdue: 'N/A',
      completionDate: 'Aug 21, 2025',
    },
    {
      id: '3',
      assignedDate: 'Sep 30, 2025',
      assignedBy: 'Jordan Kinsley',
      assignmentTitle: 'Request an update on the...',
      startDate: 'Sep 30, 2025',
      dueDate: 'Sep 30, 2025',
      status: 'In Progress',
      overdue: '5 Days',
      completionDate: 'Aug 21, 2025',
    },
    {
      id: '4',
      assignedDate: 'Oct 10, 2025',
      assignedBy: 'Emma Thompson',
      assignmentTitle: 'Please provide the latest u...',
      startDate: 'Oct 10, 2025',
      dueDate: 'Oct 10, 2025',
      status: 'Pending',
      overdue: '20 Days',
      completionDate: 'Aug 21, 2025',
    },
  ];

  const columns: Column<AssignmentLog>[] = [
    {
      key: 'assignedDate',
      label: 'Assigned Date',
      sortable: true,
    },
    {
      key: 'assignedBy',
      label: 'Assigned By',
      sortable: true,
    },
    {
      key: 'assignmentTitle',
      label: 'Assignment Title',
      sortable: true,
      render: (value) => (
        <div className="max-w-xs truncate" title={String(value)}>
          {String(value)}
        </div>
      ),
    },
    {
      key: 'startDate',
      label: 'Start Date',
      sortable: true,
    },
    {
      key: 'dueDate',
      label: 'Due Date',
      sortable: true,
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => {
        const status = value as AssignmentLog['status'];
        const colorMap = {
          Pending: 'bg-yellow-100 text-yellow-800',
          'In Progress': 'bg-blue-100 text-blue-800',
        };
        return (
          <span
            className={`px-2 py-1 text-xs font-medium rounded-full ${colorMap[status] || 'bg-gray-100 text-gray-800'}`}
          >
            {status}
          </span>
        );
      },
    },
    {
      key: 'overdue',
      label: 'Overdue',
      sortable: true,
      render: (value) => {
        const overdue = String(value);
        if (overdue === 'N/A') {
          return <span className="text-gray-500">N/A</span>;
        }
        return (
          <span className="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">
            {overdue}
          </span>
        );
      },
    },
    {
      key: 'completionDate',
      label: 'Completion Date',
      sortable: true,
    },
  ];

  const handleRowClick = (row: AssignmentLog) => {
    console.log('Assignment log clicked:', row);
    // Handle row click - could navigate to detail view
  };

  return (
    <div className="space-y-6">
      <div className="hidden justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-blackOne">
            Agent Assignment Logs
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            Track and monitor all agent assignment activities
            {filters.suite && ` for ${filters.suite}`}
            {filters.agent && ` - ${filters.agent}`}
          </p>
        </div>
      </div>

      <DataTable
        data={mockAssignmentLogs}
        columns={columns}
        onRowClick={handleRowClick}
        loading={loading}
        emptyMessage="No assignment logs found"
        rowColoring={true}
        rowColoringType="odd"
      />
    </div>
  );
};

export default AssignmentLogsPage;