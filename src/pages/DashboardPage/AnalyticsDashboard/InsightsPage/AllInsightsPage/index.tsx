import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, Filter, Search } from 'lucide-react';
import { Icons } from '@/assets/icons/DashboardIcons';
import { Button } from '@/components/ui/Button';
import Pagination from '@/components/common/Pagination';
import { useAnalyticsParams } from '@/utils/urlParams';

interface InsightData {
  id: string;
  insight: string;
  type: 'warning' | 'info' | 'suggestion';
  timestamp: string;
}

const InsightItem: React.FC<InsightData> = ({ insight, timestamp }) => {
  return (
    <div className="flex justify-between items-center p-4 bg-white rounded-lg transition-all duration-150 animate-fadeIn hover:border-primary">
      <div className="flex items-center w-full">
        <div className="mr-4 flex h-8 w-8 items-center justify-center rounded-2xl bg-[#718EBF1A]/10">
          <Icons.ChipExtractionRound className="h-[18px] w-[18px]" />
        </div>
        <div className="flex-1">
          <span className="block mb-1 text-xs font-normal text-subText sm:text-sm">
            {insight}
          </span>
          <span className="hidden text-xs text-gray-500">{timestamp}</span>
        </div>
      </div>
    </div>
  );
};

const AllInsightsPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const { filters } = useAnalyticsParams();
  const navigate = useNavigate();
  const totalPages = 4;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDownload = () => {
    console.log('Downloading report...');
  };

  const insights: InsightData[] = [
    {
      id: '1',
      insight:
        'Yesterday, 3 newly created collections objects remained unassigned for over 6 hours—Obed may want to review assignment delay triggers.',
      type: 'warning',
      timestamp: '2 hours ago',
    },
    {
      id: '2',
      insight:
        'Authorize Collin to proceed with new negotiation sequence for 18 accounts',
      type: 'suggestion',
      timestamp: '4 hours ago',
    },
    {
      id: '3',
      insight:
        'Deploy alternate Recura strategy to test better timing for Gen Z segment',
      type: 'suggestion',
      timestamp: '6 hours ago',
    },
    {
      id: '4',
      insight:
        'Noticed improved collection rate in Retail Segment — 19.2% above average. Worth deeper review.',
      type: 'info',
      timestamp: '8 hours ago',
    },
    {
      id: '5',
      insight:
        "Jane Morales' average stage duration in 'Negotiation' rose to 71 days (team avg = 4.3).",
      type: 'warning',
      timestamp: '10 hours ago',
    },
    {
      id: '6',
      insight:
        'Collection efficiency increased by 23% after implementing new workflow automation.',
      type: 'info',
      timestamp: '12 hours ago',
    },
    {
      id: '7',
      insight:
        'Consider reassigning high-priority accounts from overloaded agents to maintain performance.',
      type: 'suggestion',
      timestamp: '14 hours ago',
    },
    {
      id: '8',
      insight:
        'System detected unusual pattern in payment frequencies - recommend investigation.',
      type: 'warning',
      timestamp: '16 hours ago',
    },
  ];

  const filteredInsights = insights.filter(insight =>
    insight.insight.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="flex flex-col gap-6 h-full">
      {/* Header */}
      <div className="flex relative gap-4 justify-between items-center">
        <div className="flex gap-2 items-start">
          <button onClick={() => navigate(-1)} className="mt-1">
            <ChevronLeft className="w-5 h-5" strokeWidth={3} />
          </button>
          <div className="flex flex-col gap-1 items-start text-blackTwo">
            <h1 className="text-lg font-semibold">
              All Agent Insights
              {filters.suite && ` - ${filters.suite}`}
              {filters.agent && ` (${filters.agent})`}
            </h1>
            <p className="text-sm text-blackTwo0">
              AI-generated insights and recommendations based on daily
              performance patterns.
            </p>
          </div>
        </div>
        
        {/* Search and Filter */}
        <div className="flex gap-3 items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-6 w-6 -translate-y-1/2 transform text-[#979797]" />
            <input
              type="text"
              placeholder="Search insights..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-[44px] w-[230px] rounded-[10px] border border-[#D9D9D9] py-2 pl-12 pr-4 placeholder:text-sm placeholder:text-[#979797] focus:outline-none focus:ring-2"
            />
          </div>
          <Button className="flex h-[44px] items-center gap-2 rounded-[10px] border-2 border-primary bg-[#FDF7F6] p-4 text-primary transition-colors hover:border-0 hover:bg-primary hover:text-white">
            <span>Filter</span>
            <Filter className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Insights Content */}
      <div className="flex flex-col flex-1 gap-4">
        {filteredInsights.length === 0 ? (
          <div className="flex flex-col justify-center items-center py-12">
            <p className="text-lg text-gray-600 mb-2">No insights found</p>
            <p className="text-sm text-gray-500">
              Try adjusting your search criteria or filters
            </p>
          </div>
        ) : (
          filteredInsights.map((insight, index) => (
            <InsightItem
              key={insight.id}
              id={insight.id}
              insight={insight.insight}
              type={insight.type}
              timestamp={insight.timestamp}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        onDownload={handleDownload}
        className="mt-auto"
        downloadButtonText="Download Report"
      />
    </div>
  );
};

export default AllInsightsPage;