import React from 'react';
import SectionTitle from '@/components/common/SectionTitle';
import { ROUTES } from '@/constants/routes';
import { useAnalyticsParams } from '@/utils/urlParams';
import DailyInsightCommentary from '../DailyInsightCommentary';

const InsightsPage: React.FC = () => {
  const { filters } = useAnalyticsParams();

  // Build browse all route with current filters
  const browseAllRoute = `${ROUTES.DASHBOARD_ANALYTICS_INSIGHTS_ALL}${
    filters.suite || filters.agent 
      ? `?${new URLSearchParams(filters as Record<string, string>).toString()}`
      : ''
  }`;

  return (
    <div className="space-y-8">
      {/* Daily Insight Commentary */}
      <div className="overflow-hidden p-4 bg-white rounded-xl">
        <SectionTitle
          title="Daily Insight Commentary (From Scyra) (31insights)"
          className="mb-4"
          browseAllRoute={browseAllRoute}
        />
        <DailyInsightCommentary />
      </div>

      {/* Additional insights content can be added here */}
      {/* For example: Performance metrics, trend charts, etc. */}
    </div>
  );
};

export default InsightsPage;