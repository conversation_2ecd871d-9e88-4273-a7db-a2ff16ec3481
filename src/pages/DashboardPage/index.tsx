import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardWelcome from './DashboardWelcome';
import { ROUTES } from '../../constants/routes';
import EnhancedChatSidebar from '../../components/common/EnhancedChatSidebar';

const DashboardPage: React.FC = () => {
  const [showWelcome, setShowWelcome] = useState(true);
  const navigate = useNavigate();

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  const handleServiceSelect = () => {
    setShowWelcome(false);
    navigate(ROUTES.DASHBOARD_ANALYTICS_SETIQ_DASHBOARD);
  };

  if (showWelcome) {
    return (
      <div className="flex h-full">
        {/* Chat Interface - LHS */}
        <EnhancedChatSidebar reloadChatHistoryRef={reloadChatHistoryRef} />

        {/* Dashboard Welcome - RHS */}
        <div className="flex-1 overflow-y-auto pb-40">
          <DashboardWelcome onServiceSelect={handleServiceSelect} />
        </div>
      </div>
    );
  }

  return null;
};

export default DashboardPage;
