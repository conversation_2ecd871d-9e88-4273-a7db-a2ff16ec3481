import React, { useState, useEffect, useRef } from 'react';
import { use<PERSON><PERSON>, Submit<PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAuth } from '../../../context/AuthContext';
import { useTenant } from '@/context/TenantContext';
import { useUpdateTenantInfo } from '../../../hooks/useTenant';
import { Pencil, X } from 'lucide-react';
import { profilePlaceholder } from '@/assets/images';
import { Input } from '@/components/ui';
import { Spinner } from '@/components/common/Loader';
import Select from 'react-select';
import { ProfileFormData } from '../../../types/profile';
import { profileSettingsSchema } from '../../../lib/yup/profileValidations';
import { UpdateTenantInfoRequest } from '../../../services/tenantService';
import {
  useUpdateUserInfo,
  useGetUserFunctions,
} from '../../../services/userProfileService';
import { useAvatarUpload } from '../../../hooks/useAvatarUpload';
import { useTimezones } from '../../../hooks/useTimezones';
import * as Yup from 'yup';

// Company form data type
interface CompanyFormData {
  tenantName: string;
  tenantAddress: string;
  tenantEmail: string;
}

// Company form validation schema
const companySettingsSchema = Yup.object().shape({
  tenantName: Yup.string().required('Company name is required'),
  tenantAddress: Yup.string().required('Company address is required'),
  tenantEmail: Yup.string()
    .email('Invalid email format')
    .required('Company email is required'),
});

export const ProfileSettings: React.FC = () => {
  const { user, isLoadingUserInitials, isError } = useAuth();
  const { tenant } = useTenant();
  const updateTenantMutation = useUpdateTenantInfo();
  const updateUserInfoMutation = useUpdateUserInfo();
  const { isUploading, uploadError, handleFileSelect } = useAvatarUpload();
  const {
    timezoneOptions: apiTimezoneOptions,
    isLoading: isLoadingTimezones,
    isError: isTimezoneError,
  } = useTimezones();

  // Fetch user functions/roles from API
  const {
    data: userFunctionsData,
    isLoading: isLoadingUserFunctions,
    isError: isUserFunctionsError,
  } = useGetUserFunctions();

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isUpdatingTenant, setIsUpdatingTenant] = useState<boolean>(false);
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [updateSuccess, setUpdateSuccess] = useState<string | null>(null);

  // Auto-clear messages after 5 seconds
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (updateError || updateSuccess) {
      timeoutId = setTimeout(() => {
        setUpdateError(null);
        setUpdateSuccess(null);
      }, 5000);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [updateError, updateSuccess]);

  const userData = user;
  const currentTenant = tenant;

  // Check if user has admin role
  const isUser = userData?.userRoles?.includes('USER') || false;

  const getInitialValues = () => ({
    firstName: userData?.firstName || '',
    lastName: userData?.lastName || '',
    email: userData?.email || '',
    role: userData?.roleInCompany || '',
    timezone: userData?.timezone || '', // No default timezone
  });

  const getCompanyInitialValues = () => ({
    // tenantName: currentTenant?.tenantName || '',
    tenantName: currentTenant?.tenantAgentSuite.agentSuiteName || '',
    // tenantAddress: currentTenant?.tenantAddress || '',
    // tenantEmail: currentTenant?.tenantEmail || '',
  });

  // Personal information form configuration
  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<ProfileFormData>({
    resolver: yupResolver(profileSettingsSchema),
    defaultValues: getInitialValues(),
  });

  // Company information form configuration
  const {
    control: companyControl,
    handleSubmit: handleCompanySubmit,
    formState: { errors: companyErrors, isDirty: isCompanyDirty },
    reset: resetCompany,
  } = useForm<CompanyFormData>({
    resolver: yupResolver(companySettingsSchema),
    defaultValues: getCompanyInitialValues(),
  });

  // Update forms when user data or tenant data changes
  useEffect(() => {
    if (userData) {
      reset(getInitialValues());
    }
  }, [userData, reset]);

  useEffect(() => {
    if (currentTenant) {
      resetCompany(getCompanyInitialValues());
    }
  }, [currentTenant, resetCompany]);

  // Personal information form submission handler
  const onSubmit: SubmitHandler<ProfileFormData> = async (data) => {
    console.log('Form data being submitted:', data);
    console.log('Form is dirty:', isDirty);
    console.log('Current form values:', control._formValues);

    setIsSubmitting(true);
    setUpdateError(null);
    setUpdateSuccess(null);
    try {
      await updateUserInfoMutation({
        firstname: data.firstName,
        lastname: data.lastName,
        timezone: data.timezone,
        roleInCompany: data.role,
      });

      setUpdateSuccess('Profile updated successfully!');

      // Reset form dirty state after successful submission
      reset(data);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to update profile';
      setUpdateError(errorMessage);
      console.error('Failed to update profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Company information form submission handler
  const onCompanySubmit: SubmitHandler<CompanyFormData> = async (data) => {
    console.log('Company form data being submitted:', data);
    console.log('Company form is dirty:', isCompanyDirty);
    console.log('Current company form values:', companyControl._formValues);
    console.log('Current tenant:', currentTenant);

    if (!currentTenant?.tenantId) {
      console.error('No active tenant found');
      return;
    }

    setIsUpdatingTenant(true);
    try {
      const payload: UpdateTenantInfoRequest = {
        tenantId: currentTenant.tenantId,
        tenantName: data.tenantName,
        tenantAddress: data.tenantAddress,
        tenantEmail: data.tenantEmail,
      };

      console.log('Sending payload:', payload);
      await updateTenantMutation.mutateAsync(payload);

      // Reset form dirty state after successful submission
      resetCompany(data);
      console.log('Company form updated successfully');
    } catch (error) {
      console.error('Failed to update tenant info:', error);
    } finally {
      setIsUpdatingTenant(false);
    }
  };

  // Dynamic role options from API
  const roleOptions = userFunctionsData?.data
    ? userFunctionsData.data.map((role: string) => ({
        value: role,
        label: role.charAt(0).toUpperCase() + role.slice(1).toLowerCase(),
      }))
    : [
        // Fallback options if API fails
        { value: 'USER', label: 'User' },
        { value: 'ADMIN', label: 'Admin' },
      ];

  // Fallback timezone options if API fails
  const fallbackTimezoneOptions: { value: string; label: string }[] = [];

  const timezoneOptions = isTimezoneError
    ? fallbackTimezoneOptions
    : apiTimezoneOptions;

  // Custom styles for react-select
  const selectStyles = {
    control: (provided: any) => ({
      ...provided,
      height: '40px',
      border: '1px solid #DFEAF2',
      borderRadius: '6px',
      boxShadow: 'none',
      '&:hover': {
        border: '1px solid #DFEAF2',
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#FF5C02'
        : state.isFocused
          ? '#FFF5F0'
          : 'white',
      color: state.isSelected ? 'white' : '#374151',
      '&:hover': {
        backgroundColor: state.isSelected ? '#FF5C02' : '#FFF5F0',
      },
    }),
    menu: (provided: any) => ({
      ...provided,
      border: '1px solid #DFEAF2',
      borderRadius: '6px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    }),
  };

  if (isLoadingUserInitials) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center space-y-4">
          <Spinner className="w-8 h-8" />
          <p className="text-sm text-gray-500">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="flex justify-center items-center mx-auto mb-4 w-16 h-16 bg-red-100 rounded-full">
            <svg
              className="w-8 h-8 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-medium text-blackOne">
            Failed to load profile
          </h3>
          <p className="mb-4 text-sm text-gray-500">
            Unable to load your profile information. Please try refreshing the
            page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 text-sm text-white rounded-md bg-primary hover:bg-primary/90"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-8">
      {/* Error and Success Messages - Top of Page */}
      {updateError && (
        <div className="relative overflow-hidden rounded-lg border border-[#fecaca] bg-gradient-to-r from-[#fef2f2] to-[#fee2e2] p-4 shadow-sm">
          <div className="flex gap-3 items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-[#dc2626]"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-[#991b1b]">
                {updateError}
              </p>
            </div>
            <button
              onClick={() => setUpdateError(null)}
              className="flex-shrink-0 text-[#f87171] transition-colors hover:text-[#dc2626]"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          {/* Progress bar */}
          <div className="absolute bottom-0 left-0 h-1 animate-pulse bg-[#fca5a5]"></div>
        </div>
      )}

      {updateSuccess && (
        <div className="relative overflow-hidden rounded-xl border border-[#bbf7d0] bg-gradient-to-r from-[#f0fdf4] to-[#dcfce7] p-4">
          <div className="flex gap-3 items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-6 w-6 text-[#16a34a]"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-[#166534]">
                {updateSuccess}
              </p>
            </div>
            <button
              onClick={() => setUpdateSuccess(null)}
              className="flex-shrink-0 text-[#4ade80] transition-colors hover:text-[#16a34a]"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          {/* Progress bar */}
          <div className="absolute bottom-0 left-0 h-1 animate-pulse bg-[#86efac]"></div>
        </div>
      )}

      {/* Personal Information Section */}
      <div>
        <h2 className="mb-6 text-xl font-semibold text-blackOne">
          Personal Information
        </h2>

        <div className="flex flex-col items-start space-y-6">
          {/* Profile Picture */}
          <div className="flex flex-col gap-2 items-start w-full">
            <div className="relative shrink-0">
              <div className="flex h-20 w-20 items-center justify-center overflow-hidden rounded-full md:h-[110px] lg:w-[110px]">
                <img
                  src={userData?.profilePicture || profilePlaceholder}
                  alt="Profile"
                  className="object-cover flex-shrink-0 w-full h-full"
                />
                {isUploading && (
                  <div className="flex absolute inset-0 justify-center items-center bg-black bg-opacity-50 rounded-full">
                    <Spinner className="w-6 h-6 text-white" />
                  </div>
                )}
              </div>
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
                className="absolute bottom-2 right-1 flex h-[25px] w-[25px] items-center justify-center rounded-full bg-primary text-xs text-white disabled:bg-gray-400"
              >
                <Pencil className="w-3 h-3" />
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={async (e) => {
                  await handleFileSelect(e);
                  // Clear the input to allow selecting the same file again
                  e.target.value = '';
                }}
                className="hidden"
              />
            </div>{' '}
            {uploadError && (
              <p className="text-xs text-red-500">{uploadError}</p>
            )}
          </div>

          {/* Personal Information Form */}
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-4 w-full max-w-2xl"
          >
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  First name
                </label>
                <Controller
                  name="firstName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="text"
                      className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                        errors.firstName ? 'border-red-500' : 'border-[#DFEAF2]'
                      }`}
                      placeholder="Enter first name"
                    />
                  )}
                />
                {errors.firstName && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.firstName.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  Role
                </label>
                <Controller
                  name="role"
                  control={control}
                  render={({ field }) => (
                    <Select
                      options={roleOptions}
                      styles={{
                        ...selectStyles,
                        control: (provided: any) => ({
                          ...provided,
                          height: '40px',
                          border: errors.role
                            ? '1px solid #ef4444'
                            : '1px solid #DFEAF2',
                          borderRadius: '6px',
                          boxShadow: 'none',
                          '&:hover': {
                            border: errors.role
                              ? '1px solid #ef4444'
                              : '1px solid #DFEAF2',
                          },
                        }),
                      }}
                      value={
                        roleOptions.find(
                          (option: { value: string; label: string }) =>
                            option.value === field.value
                        ) || null
                      }
                      onChange={(
                        selectedOption: { value: string; label: string } | null
                      ) => {
                        console.log('Role selected:', selectedOption?.value);
                        field.onChange(selectedOption?.value || '');
                      }}
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                      placeholder={
                        isLoadingUserFunctions
                          ? 'Loading roles...'
                          : isUserFunctionsError
                            ? 'Select role (limited options)'
                            : 'Select role'
                      }
                      isSearchable={false}
                      isLoading={isLoadingUserFunctions}
                    />
                  )}
                />
                {errors.role && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.role.message}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  Last name
                </label>
                <Controller
                  name="lastName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="text"
                      className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                        errors.lastName ? 'border-red-500' : 'border-[#DFEAF2]'
                      }`}
                      placeholder="Enter last name"
                    />
                  )}
                />
                {errors.lastName && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.lastName.message}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  Timezone
                </label>
                <Controller
                  name="timezone"
                  control={control}
                  render={({ field }) => (
                    <Select
                      options={timezoneOptions}
                      styles={{
                        ...selectStyles,
                        control: (provided: any) => ({
                          ...provided,
                          height: '40px',
                          border: errors.timezone
                            ? '1px solid #ef4444'
                            : '1px solid #DFEAF2',
                          borderRadius: '6px',
                          boxShadow: 'none',
                          '&:hover': {
                            border: errors.timezone
                              ? '1px solid #ef4444'
                              : '1px solid #DFEAF2',
                          },
                        }),
                      }}
                      value={timezoneOptions.find(
                        (option: { value: string; label: string }) =>
                          option.value === field.value
                      )}
                      onChange={(selectedOption) =>
                        field.onChange(selectedOption?.value)
                      }
                      components={{
                        IndicatorSeparator: () => null,
                      }}
                      placeholder={
                        isLoadingTimezones
                          ? 'Loading timezones...'
                          : isTimezoneError
                            ? 'Select timezone (limited options)'
                            : 'Select timezone'
                      }
                      isSearchable={true}
                    />
                  )}
                />
                {errors.timezone && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.timezone.message}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-[13px] text-subText">
                  Email
                </label>
                <Controller
                  name="email"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="email"
                      className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                        errors.email ? 'border-red-500' : 'border-[#DFEAF2]'
                      }`}
                      placeholder="Enter email address"
                    />
                  )}
                />
                {errors.email && (
                  <p className="mt-1 text-xs text-red-500">
                    {errors.email.message}
                  </p>
                )}
              </div>
            </div>

            <div className="flex justify-start">
              <button
                type="submit"
                disabled={!isDirty || isSubmitting}
                className={`h-10 w-[130px] rounded-md px-6 py-2 text-white transition-colors ${
                  !isDirty || isSubmitting
                    ? 'cursor-not-allowed bg-gray-400'
                    : 'bg-grayTen hover:bg-gray-700'
                }`}
              >
                {isSubmitting ? (
                  <div className="flex justify-center items-center">
                    <Spinner className="w-4 h-4" />
                  </div>
                ) : (
                  'Save changes'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Company Information Section */}
      <div className="hidden space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-blackOne">
            Company Information
          </h2>
          {isUser && (
            <span className="hidden px-3 py-1 text-sm text-gray-500 bg-gray-100 rounded-full">
              View Only
            </span>
          )}
        </div>

        <form
          onSubmit={handleCompanySubmit(onCompanySubmit)}
          className="space-y-4 w-full max-w-2xl"
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-2 block text-[13px] text-subText">
                Company Name
              </label>
              <Controller
                name="tenantName"
                control={companyControl}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    disabled={isUser}
                    className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                      isUser
                        ? 'cursor-not-allowed bg-gray-50 text-gray-500'
                        : companyErrors.tenantName
                          ? 'border-red-500'
                          : 'border-[#DFEAF2]'
                    }`}
                    placeholder={isUser ? 'Company name' : 'Enter company name'}
                  />
                )}
              />
              {companyErrors.tenantName && (
                <p className="mt-1 text-xs text-red-500">
                  {companyErrors.tenantName.message}
                </p>
              )}
            </div>

            <div>
              <label className="mb-2 block text-[13px] text-subText">
                Company Email
              </label>
              <Controller
                name="tenantEmail"
                control={companyControl}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="email"
                    disabled={isUser}
                    className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                      isUser
                        ? 'cursor-not-allowed bg-gray-50 text-gray-500'
                        : companyErrors.tenantEmail
                          ? 'border-red-500'
                          : 'border-[#DFEAF2]'
                    }`}
                    placeholder={
                      isUser ? 'Company email' : 'Enter company email'
                    }
                  />
                )}
              />
              {companyErrors.tenantEmail && (
                <p className="mt-1 text-xs text-red-500">
                  {companyErrors.tenantEmail.message}
                </p>
              )}
            </div>
          </div>

          <div>
            <label className="mb-2 block text-[13px] text-subText">
              Company Address
            </label>
            <Controller
              name="tenantAddress"
              control={companyControl}
              render={({ field }) => (
                <Input
                  {...field}
                  type="text"
                  disabled={isUser}
                  className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                    isUser
                      ? 'cursor-not-allowed bg-gray-50 text-gray-500'
                      : companyErrors.tenantAddress
                        ? 'border-red-500'
                        : 'border-[#DFEAF2]'
                  }`}
                  placeholder={
                    isUser ? 'Company address' : 'Enter company address'
                  }
                />
              )}
            />
            {companyErrors.tenantAddress && (
              <p className="mt-1 text-xs text-red-500">
                {companyErrors.tenantAddress.message}
              </p>
            )}
          </div>

          {!isUser && (
            <div className="flex justify-start">
              <button
                type="submit"
                disabled={!isCompanyDirty || isUpdatingTenant}
                className={`h-10 w-[130px] rounded-md px-6 py-2 text-white transition-colors ${
                  !isCompanyDirty || isUpdatingTenant
                    ? 'cursor-not-allowed bg-gray-400'
                    : 'bg-grayTen hover:bg-gray-700'
                }`}
              >
                {isUpdatingTenant ? (
                  <div className="flex items-center">
                    <Spinner className="w-4 h-4" />
                    <span>Saving...</span>
                  </div>
                ) : (
                  'Save changes'
                )}
              </button>
              {/* Debug info for company form */}
              <div className="ml-4 text-xs text-gray-500">
                <p>Company form dirty: {isCompanyDirty ? 'Yes' : 'No'}</p>
                <p>Updating tenant: {isUpdatingTenant ? 'Yes' : 'No'}</p>
                <p>User role: {isUser ? 'User' : 'Admin'}</p>
                <p>
                  Company name:{' '}
                  {companyControl._formValues?.tenantName || 'None'}
                </p>
                <p>
                  Company email:{' '}
                  {companyControl._formValues?.tenantEmail || 'None'}
                </p>
                <p>
                  Company address:{' '}
                  {companyControl._formValues?.tenantAddress || 'None'}
                </p>
              </div>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};
