import React, { useState } from 'react';

export const NotificationSettings: React.FC = () => {
  const [newsUpdates, setNewsUpdates] = useState(true);
  const [tipsAndTutorials, setTipsAndTutorials] = useState(false);
  const [userResearch, setUserResearch] = useState(false);
  const [notificationLevel, setNotificationLevel] = useState('none');
  const [emailFrequency, setEmailFrequency] = useState('daily');

  return (
    <div className="space-y-6">
      <h2 className="mb-4 text-2xl font-semibold text-black">Notifications</h2>

      {/* Notifications from us */}
      <div>
        <h3 className="mb-2 text-lg font-medium text-black">
          Notifications from us
        </h3>
        <p className="mb-4 text-sm text-subText">
          Receive the latest news, updates, and industry tutorials from us.
        </p>

        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="news-updates"
              checked={newsUpdates}
              onChange={e => setNewsUpdates(e.target.checked)}
              className="h-6 w-6 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <div>
              <label
                htmlFor="news-updates"
                className="text-sm font-medium text-black"
              >
                News and updates
              </label>
              <p className="text-sm text-subText">
                News about product and feature updates.
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="tips-tutorials"
              checked={tipsAndTutorials}
              onChange={e => setTipsAndTutorials(e.target.checked)}
              className="h-6 w-6 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <div>
              <label
                htmlFor="tips-tutorials"
                className="text-sm font-medium text-black"
              >
                Tips and tutorials
              </label>
              <p className="text-sm text-subText">
                Tips on getting more out of PivoTL
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="user-research"
              checked={userResearch}
              onChange={e => setUserResearch(e.target.checked)}
              className="h-6 w-6 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <div>
              <label
                htmlFor="user-research"
                className="text-sm font-medium text-black"
              >
                User research
              </label>
              <p className="text-sm text-subText">
                Data results to help you better understand your users.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Modifications */}
      <div>
        <h3 className="mb-1 text-lg font-medium text-black">Modifications</h3>
        <p className="mb-4 text-sm text-subText">
          These are modifications made to knowledge base documents
        </p>

        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <input
              type="radio"
              id="no-notify"
              name="modifications"
              value="none"
              checked={notificationLevel === 'none'}
              onChange={e => setNotificationLevel(e.target.value)}
              className="h-6 w-6 border-gray-300 text-primary focus:ring-primary"
            />
            <div>
              <label
                htmlFor="no-notify"
                className="text-sm font-medium text-black"
              >
                Do not notify me
              </label>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="radio"
              id="mentions-only"
              name="modifications"
              value="mentions"
              checked={notificationLevel === 'mentions'}
              onChange={e => setNotificationLevel(e.target.value)}
              className="h-6 w-6 border-gray-300 text-primary focus:ring-primary"
            />
            <div>
              <label
                htmlFor="mentions-only"
                className="text-sm font-medium text-black"
              >
                Mentions only
              </label>
              <p className="text-sm text-subText">
                Only notify me if I'm mentioned in a comment.
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="radio"
              id="all-comments"
              name="modifications"
              value="all"
              checked={notificationLevel === 'all'}
              onChange={e => setNotificationLevel(e.target.value)}
              className="h-6 w-6 border-gray-300 text-primary focus:ring-primary"
            />
            <div>
              <label
                htmlFor="all-comments"
                className="text-sm font-medium text-black"
              >
                All comments
              </label>
              <p className="text-sm text-subText">
                Notify me for all comments on my posts.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Email subscriptions */}
      <div>
        <h3 className="mb-2 text-lg font-medium text-black">
          Email subscriptions
        </h3>
        <div className="mb-4">
          <p className="mb-1 text-sm font-medium text-black">
            Activity summary
          </p>
          <p className="text-sm text-subText">
            Schedule email summaries of tasks that PivoTL Agents have performed.
          </p>
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <input
              type="radio"
              id="daily"
              name="email-frequency"
              value="daily"
              checked={emailFrequency === 'daily'}
              onChange={e => setEmailFrequency(e.target.value)}
              className="h-6 w-6 border-gray-300 text-primary focus:ring-primary"
            />
            <label htmlFor="daily" className="text-sm font-medium text-black">
              Daily
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="radio"
              id="weekly"
              name="email-frequency"
              value="weekly"
              checked={emailFrequency === 'weekly'}
              onChange={e => setEmailFrequency(e.target.value)}
              className="h-6 w-6 border-gray-300 text-primary focus:ring-primary"
            />
            <label htmlFor="weekly" className="text-sm font-medium text-black">
              Weekly
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="radio"
              id="never"
              name="email-frequency"
              value="never"
              checked={emailFrequency === 'never'}
              onChange={e => setEmailFrequency(e.target.value)}
              className="h-6 w-6 border-gray-300 text-primary focus:ring-primary"
            />
            <label htmlFor="never" className="text-sm font-medium text-black">
              Never
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};
