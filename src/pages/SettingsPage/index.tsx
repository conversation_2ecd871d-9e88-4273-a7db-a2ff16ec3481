import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ProfileSettings } from './components/ProfileSettings';
import { NotificationSettings } from './components/NotificationSettings';
import { BillingSettings } from './components/BillingSettings';
import { MembersSettings } from './components/MembersSettings';
import AppContainer from '../../components/common/AppContainer';

const PivotlSettingsPage: React.FC = () => {
  return (
    <div className="h-full overflow-y-auto">
      <AppContainer>
        <Routes>
          <Route path="/" element={<Navigate to="profile" replace />} />
          <Route path="/profile" element={<ProfileSettings />} />
          <Route path="/notifications" element={<NotificationSettings />} />
          <Route path="/billing" element={<BillingSettings />} />
          <Route path="/members" element={<MembersSettings />} />
        </Routes>
      </AppContainer>
    </div>
  );
};

export default PivotlSettingsPage;
