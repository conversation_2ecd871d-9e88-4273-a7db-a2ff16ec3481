import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import type { TenantInfo, ClaimedAgentSuite } from "../types/user";

type TenantContextType = {
  tenant: TenantInfo | null;
  tenantId: string | null;
  setTenantId: (tenantId: string | null) => void;
  activeAgent: string | null;
  setActiveAgent: (agentKey: string) => void;
  isLoading: boolean;
  claimedSuites: ClaimedAgentSuite[];
};

const TenantContext = createContext<TenantContextType | undefined>(undefined);

const ACTIVE_AGENT_STORAGE_KEY = "agentous_active_agent";
const TENANT_ID_STORAGE_KEY = "agentous_tenant_id";

export const TenantProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [activeAgent, setActiveAgentState] = useState<string | null>("colton");
  const [storedTenantId, setStoredTenantId] = useState<string | null>(null);
  const [isReady, setIsReady] = useState<boolean>(false);

  // Load stored values from localStorage once
  useEffect(() => {
    try {
      const storedAgent = localStorage.getItem(ACTIVE_AGENT_STORAGE_KEY);
      const storedTenant = localStorage.getItem(TENANT_ID_STORAGE_KEY);

      if (storedAgent) {
        setActiveAgentState(storedAgent);
      }
      if (storedTenant) {
        setStoredTenantId(storedTenant);
      }
    } catch (error) {
      console.error("Error loading data from localStorage:", error);
      localStorage.removeItem(ACTIVE_AGENT_STORAGE_KEY);
      localStorage.removeItem(TENANT_ID_STORAGE_KEY);
    } finally {
      setIsReady(true);
    }
  }, []);

  // For now, return null values for tenant and claimedSuites since we're simplifying
  const tenant: TenantInfo | null = null;
  const tenantId = storedTenantId;
  const claimedSuites: ClaimedAgentSuite[] = [];

  const setTenantId = (newTenantId: string | null) => {
    setStoredTenantId(newTenantId);
    try {
      if (newTenantId) {
        localStorage.setItem(TENANT_ID_STORAGE_KEY, newTenantId);
      } else {
        localStorage.removeItem(TENANT_ID_STORAGE_KEY);
      }
    } catch (error) {
      console.error("Error saving tenant ID to localStorage:", error);
    }
  };

  const setActiveAgent = (agentKey: string) => {
    setActiveAgentState(agentKey);
    try {
      if (agentKey) {
        localStorage.setItem(ACTIVE_AGENT_STORAGE_KEY, agentKey);
      } else {
        localStorage.removeItem(ACTIVE_AGENT_STORAGE_KEY);
      }
    } catch (error) {
      console.error("Error saving active agent to localStorage:", error);
    }
  };

  const value = useMemo<TenantContextType>(
    () => ({
      tenant,
      tenantId,
      setTenantId,
      activeAgent,
      setActiveAgent,
      isLoading: !isReady,
      claimedSuites,
    }),
    [tenant, tenantId, activeAgent, isReady, claimedSuites]
  );

  return (
    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
  );
};

export const useTenant = (): TenantContextType => {
  const ctx = useContext(TenantContext);
  if (!ctx) throw new Error("useTenant must be used within a TenantProvider");
  return ctx;
};
